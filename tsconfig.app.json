{"extends": ["@tsconfig/node22/tsconfig.json"], "include": ["env.d.ts", "src/**/*", "src/**/*.tsx", "types/**/*"], "exclude": ["src/**/__tests__/*"], "compilerOptions": {"target": "ES2020", "lib": ["ES2020", "DOM", "DOM.Iterable"], "allowJs": false, "skipLibCheck": true, "esModuleInterop": false, "allowSyntheticDefaultImports": true, "strict": true, "forceConsistentCasingInFileNames": true, "module": "ESNext", "moduleResolution": "bundler", "resolveJsonModule": true, "isolatedModules": true, "noEmit": true, "jsx": "react-jsx", "tsBuildInfoFile": "./node_modules/.tmp/tsconfig.app.tsbuildinfo", "paths": {"@/*": ["./src/*"]}}}
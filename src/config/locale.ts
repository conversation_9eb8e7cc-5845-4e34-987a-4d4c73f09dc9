// 定义 Locale 类型
type I18nLocale = string;

// 定义应用支持的语言常量
export const Locale = {
  ZH_MO: 'zh_MO',
  EN_US: 'en_US',
};

// 创建支持的语言列表
const supportLocales: I18nLocale[] = [];
if (import.meta.env.VITE_SUPPORT_LOCALES) {
  const locales = import.meta.env.VITE_SUPPORT_LOCALES.split(',').map(
    (l: string) => l.trim() as I18nLocale
  );
  supportLocales.push(...locales);
} else {
  supportLocales.push(...Object.values(Locale));
}

export const SUPPORT_LOCALES = Object.freeze(supportLocales);
export const FALLBACK_LOCALE: I18nLocale =
  (import.meta.env.VITE_FALLBACK_LOCALE as I18nLocale) || Locale.ZH_MO;

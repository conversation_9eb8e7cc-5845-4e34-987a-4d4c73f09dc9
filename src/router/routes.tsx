import React from 'react';
import type { RouteObject } from 'react-router-dom';
import { PUBLIC_PATH } from '@/config/router';

// 懒加载组件
const TheRoot = React.lazy(() => import('@/components/the-root'));
const Home = React.lazy(() => import('@/views/home'));

export enum RouterName {
  Root = 'Root',
  Home = 'Home',
}

export const routes: RouteObject[] = [
  {
    path: PUBLIC_PATH,
    element: <TheRoot />,
    children: [
      {
        index: true,
        element: <Home />,
        // 在React Router中，我们使用loader来处理路由元数据
        loader: () => ({ requiresAuth: false }),
      },
      {
        path: 'home',
        element: <Home />,
        // 在React Router中，我们使用loader来处理路由元数据
        loader: () => ({ requiresAuth: false }),
      },
    ],
  },
];

import type { RouteObject } from 'react-router-dom';
import { PUBLIC_PATH } from '@/config/router';

// 直接导入组件，避免懒加载在SSR中的问题
import TheRoot from '@/components/the-root';
import Home from '@/views/home';

export enum RouterName {
  Root = 'Root',
  Home = 'Home',
}

export const routes: RouteObject[] = [
  {
    path: PUBLIC_PATH,
    element: <TheRoot />,
    children: [
      {
        index: true,
        element: <Home />,
        // 在React Router中，我们使用loader来处理路由元数据
        loader: () => ({ requiresAuth: false }),
      },
      {
        path: 'home',
        element: <Home />,
        // 在React Router中，我们使用loader来处理路由元数据
        loader: () => ({ requiresAuth: false }),
      },
    ],
  },
];

import type { RouteRecordRaw } from 'vue-router';
import { PUBLIC_PATH } from '@/config/public-path';
import { RouterName } from '@/config/router';
import TheRoot from '@/components/the-root.vue';

export const routes = Object.freeze<RouteRecordRaw[]>([
  {
    path: PUBLIC_PATH,
    name: RouterName.Root,
    component: TheRoot,
    children: [
      {
        path: 'home',
        name: RouterName.Home,
        component: () => import('@/views/home'),
        meta: {
          requiresAuth: false, // TODO：e2e测试用
        },
      },
    ],
  },
]);

import { isArray, pick } from 'radash';
import { isNil, isPlainObject } from './is';

/**
 * 过滤数组中的nil值（null或undefined）。
 *
 * @param arr - 待过滤的数组。
 * @returns 过滤后的数组，不包含nil值。
 */
function arrayNilFilter(arr: Array<unknown>): Array<unknown> {
  return arr.filter((o) => !isNil(o));
}

/**
 * 从对象中过滤掉值为nil（null或undefined）的键。
 *
 * @param {Object} obj - 待过滤的对象。
 * @returns {Object} 过滤后的对象，不包含值为nil的键。
 */
export function objectNilFilter(obj: { [key: string]: unknown }): {
  [key: string]: unknown;
} {
  return pick(
    obj,
    Object.keys(obj).filter((key) => !isNil(obj[key]))
  );
}

/**
 * 递归过滤数组或对象中的nil值。
 *
 * @param target - 可能为数组、对象或其他类型的值。
 * @returns 过滤后的值，如果是数组或对象，则去除了nil值。
 */
export function nilFilter(target: unknown): unknown {
  if (isArray(target)) {
    const filteredArray = arrayNilFilter(target as unknown[]);
    return filteredArray.map((item) => nilFilter(item));
  }

  if (isPlainObject(target)) {
    const filteredObj = objectNilFilter(target as { [key: string]: unknown });
    Object.keys(filteredObj).forEach((key) => {
      filteredObj[key] = nilFilter(filteredObj[key]);
    });
    return filteredObj;
  }

  return target;
}

/**
 * 深度克隆对象或数组。
 *
 * @param value - 要克隆的值。
 * @returns 克隆后的值。
 */
export function cloneDeep<T>(value: T): T {
  // 处理基本类型和null
  if (value === null || typeof value !== 'object') {
    return value;
  }

  // 处理日期
  if (value instanceof Date) {
    return new Date(value.getTime()) as unknown as T;
  }

  // 处理数组
  if (Array.isArray(value)) {
    return value.map((item) => cloneDeep(item)) as unknown as T;
  }

  // 处理普通对象
  if (Object.prototype.toString.call(value) === '[object Object]') {
    const result = {} as Record<string, unknown>;
    for (const key in value) {
      if (Object.prototype.hasOwnProperty.call(value, key)) {
        result[key] = cloneDeep((value as Record<string, unknown>)[key]);
      }
    }
    return result as unknown as T;
  }

  // 其他类型直接返回
  return value;
}

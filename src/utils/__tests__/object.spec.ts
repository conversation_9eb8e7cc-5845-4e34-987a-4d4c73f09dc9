import { describe, expect, it } from 'vitest';
import { objectNilFilter, nilFilter, cloneDeep } from '../object';

describe('objectNilFilter', () => {
  it('应该过滤掉对象中的null和undefined值', () => {
    const obj = {
      a: 1,
      b: 'string',
      c: null,
      d: undefined,
      e: 0,
      f: '',
      g: false,
    };

    const result = objectNilFilter(obj);

    // 验证结果中不包含null和undefined值
    expect(result).toEqual({
      a: 1,
      b: 'string',
      e: 0,
      f: '',
      g: false,
    });
    // 验证原始对象没有被修改
    expect(obj).toEqual({
      a: 1,
      b: 'string',
      c: null,
      d: undefined,
      e: 0,
      f: '',
      g: false,
    });
  });

  it('应该处理空对象', () => {
    const obj = {};
    const result = objectNilFilter(obj);
    expect(result).toEqual({});
  });

  it('应该保留假值（除了null和undefined）', () => {
    const obj = {
      a: 0,
      b: '',
      c: false,
    };
    const result = objectNilFilter(obj);
    expect(result).toEqual({
      a: 0,
      b: '',
      c: false,
    });
  });
});

describe('nilFilter', () => {
  it('应该递归过滤对象中的null和undefined值', () => {
    const obj = {
      a: 1,
      b: null,
      c: {
        d: 'string',
        e: undefined,
        f: {
          g: null,
          h: 2,
        },
      },
    };

    const result = nilFilter(obj);

    expect(result).toEqual({
      a: 1,
      c: {
        d: 'string',
        f: {
          h: 2,
        },
      },
    });
  });

  it('应该递归过滤数组中的null和undefined值', () => {
    const arr = [1, null, 'string', undefined, [1, null, 2], { a: 1, b: null }];

    const result = nilFilter(arr);

    expect(result).toEqual([1, 'string', [1, 2], { a: 1 }]);
  });

  it('应该处理非数组和非对象的值', () => {
    expect(nilFilter(1)).toBe(1);
    expect(nilFilter('string')).toBe('string');
    expect(nilFilter(false)).toBe(false);
    expect(nilFilter(0)).toBe(0);
    expect(nilFilter('')).toBe('');
  });

  it('应该处理null和undefined', () => {
    expect(nilFilter(null)).toBe(null);
    expect(nilFilter(undefined)).toBe(undefined);
  });

  it('应该处理复杂的嵌套结构', () => {
    const complex = {
      a: [1, null, { b: undefined, c: 3 }],
      d: null,
      e: {
        f: [null, 4, undefined],
        g: { h: null },
      },
    };

    const result = nilFilter(complex);

    expect(result).toEqual({
      a: [1, { c: 3 }],
      e: {
        f: [4],
        g: {},
      },
    });
  });
});

describe('cloneDeep', () => {
  it('应该深度克隆基本类型', () => {
    expect(cloneDeep(1)).toBe(1);
    expect(cloneDeep('string')).toBe('string');
    expect(cloneDeep(true)).toBe(true);
    expect(cloneDeep(null)).toBe(null);
    expect(cloneDeep(undefined)).toBe(undefined);
  });

  it('应该深度克隆数组', () => {
    const original = [1, 2, [3, 4]];
    const clone = cloneDeep(original);

    // 验证值相等
    expect(clone).toEqual(original);
    // 验证是不同的引用
    expect(clone).not.toBe(original);
    // 验证嵌套数组也是不同的引用
    expect(clone[2]).not.toBe(original[2]);
  });

  it('应该深度克隆对象', () => {
    const original = {
      a: 1,
      b: {
        c: 2,
        d: {
          e: 3,
        },
      },
    };
    const clone = cloneDeep(original);

    // 验证值相等
    expect(clone).toEqual(original);
    // 验证是不同的引用
    expect(clone).not.toBe(original);
    // 验证嵌套对象也是不同的引用
    expect(clone.b).not.toBe(original.b);
    expect(clone.b.d).not.toBe(original.b.d);
  });

  it('应该深度克隆日期对象', () => {
    const original = new Date();
    const clone = cloneDeep(original);

    // 验证值相等
    expect(clone.getTime()).toBe(original.getTime());
    // 验证是不同的引用
    expect(clone).not.toBe(original);
  });

  it('应该深度克隆包含不同类型的复杂对象', () => {
    const date = new Date();
    const original = {
      a: 1,
      b: 'string',
      c: true,
      d: date,
      e: [1, 2, { f: 3 }],
      g: {
        h: 4,
        i: [5, 6],
      },
    };
    const clone = cloneDeep(original);

    // 验证值相等
    expect(clone).toEqual(original);
    // 验证是不同的引用
    expect(clone).not.toBe(original);
    // 验证嵌套对象和数组也是不同的引用
    expect(clone.d).not.toBe(original.d);
    expect(clone.e).not.toBe(original.e);
    expect(clone.e[2]).not.toBe(original.e[2]);
    expect(clone.g).not.toBe(original.g);
    expect(clone.g.i).not.toBe(original.g.i);
  });
});

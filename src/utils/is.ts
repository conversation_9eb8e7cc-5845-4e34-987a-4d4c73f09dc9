import { isObject } from 'radash';

/**
 * 检查是否在服务器端。
 *
 * @returns 如果在服务器端，则返回true，否则返回false。
 */
export function isServer() {
  return import.meta.env.SSR;
}

/**
 * 检查值是否为FormData。
 *
 * @param thing - 要检查的值。
 * @returns 如果值为FormData，则返回true，否则返回false。
 */
export function isFormData(thing: unknown): boolean {
  return thing instanceof FormData;
}

/**
 * 检查值是否为nil（null或undefined）。
 *
 * @param value - 要检查的值。
 * @returns 如果值为null或undefined，则返回true，否则返回false。
 */
export function isNil(value: unknown): boolean {
  return value === null || value === undefined;
}

/**
 * 检查值是否为普通对象。
 *
 * @param value - 要检查的值。
 * @returns 如果值为普通对象，则返回true，否则返回false。
 */
export function isPlainObject(value: unknown): boolean {
  if (isNil(value)) return false;
  if (!isObject(value)) return false;

  // 检查原型链
  const proto = Object.getPrototypeOf(value);
  return proto === Object.prototype || proto === null;
}

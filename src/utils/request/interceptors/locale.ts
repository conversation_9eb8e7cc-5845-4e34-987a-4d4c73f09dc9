import type { InternalAxiosRequestConfig } from 'axios';
import { i18n } from '@/i18n';
import { useI18nStore } from '@/stores/i18n';

/**
 * 该函数用作请求拦截器，用于向请求参数中注入用户的语言偏好设置。
 * 在客户端环境中，从 Pinia store 获取语言设置；在服务端环境中，使用默认语言。
 *
 * @returns {Function} 返回一个新的函数，该函数接受一个请求对象并返回一个修改后的请求对象，
 *                    其中包含了原有的请求参数及语言偏好参数。
 */
export function interceptorLocale(): (
  config: InternalAxiosRequestConfig
) => InternalAxiosRequestConfig {
  return (config: InternalAxiosRequestConfig) => {
    // 在React中，我们从i18n实例获取当前语言
    // 或者从Zustand store获取
    const currentLang = i18n.language || useI18nStore.getState().locale;

    const params = {
      ...config.params,
      lang: currentLang,
    };
    Object.assign(config, { params });

    return config;
  };
}

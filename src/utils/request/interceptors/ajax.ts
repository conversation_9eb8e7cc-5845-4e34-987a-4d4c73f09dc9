import type { InternalAxiosRequestConfig } from 'axios';

/**
 * 该函数拦截 AJAX 请求的配置对象，添加一个自定义的请求头，用于指示这是一个 AJAX 请求。
 *
 * @param config - AJAX 请求的配置对象，它应该包含headers属性用于存放请求头信息。
 * @returns 返回一个新的配置对象，其中包含了原有的headers并添加了'X-Requested-With': 'XMLHttpRequest'头。
 */
export function interceptorAjax(
  config: InternalAxiosRequestConfig
): InternalAxiosRequestConfig {
  const headers = {
    ...config.headers,
    'X-Requested-With': 'XMLHttpRequest',
  };
  Object.assign(config, { headers });

  return config;
}

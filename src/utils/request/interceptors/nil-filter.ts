import type { InternalAxiosRequestConfig } from 'axios';
import { objectNilFilter, nilFilter, cloneDeep } from '@/utils/object';

/**
 * 拦截器函数，用于过滤请求配置中的nil值，包括params和data。
 *
 * @param {Object} config - 请求的配置对象。
 * @returns {Object} 更新后的配置对象，其中nil值已被过滤。
 */
export function interceptorNilFilter(
  config: InternalAxiosRequestConfig
): InternalAxiosRequestConfig {
  const params = objectNilFilter(config.params || {});
  Object.assign(config, {
    params,
  });

  if (config.data instanceof FormData) {
    return config;
  }

  const data = nilFilter(cloneDeep(config.data));
  Object.assign(config, {
    data,
  });

  return config;
}

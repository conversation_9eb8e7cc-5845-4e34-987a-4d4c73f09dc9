import type { InternalAxiosRequestConfig } from 'axios';
import { isArray, isEmpty, isObject, sort } from 'radash';
import { md5 } from 'js-md5';
import { isFormData, isNil } from '@/utils/is';

function pairsQuery(query: Record<string, unknown>): Array<[string, unknown]> {
  return Object.entries(query || {}).map(([k, v]: [string, unknown]) => {
    if (isArray(v)) {
      return [k, isEmpty(v) ? null : v[0]];
    }
    return [k, v];
  });
}

// 处理Body为正常的JSON键值对
function pairsBodyGeneral(body: unknown): Array<[string, unknown]> {
  if (isFormData(body) || isArray(body)) {
    return [];
  }

  return Object.entries((body as Record<string, unknown>) || {}).map(
    ([k, v]: [string, unknown]) => {
      if (isObject(v)) {
        return [k, JSON.stringify(v)];
      }
      return [k, v];
    }
  );
}

// 处理Body为JSON数组格式
function pairsBodyArray(body: unknown): Array<[string, unknown]> {
  if (isFormData(body) || !isArray(body)) {
    return [];
  }

  let pairs: Array<[string, unknown]> = [];

  (body as Array<Record<string, unknown>>).forEach((item) => {
    const itemPairs = Object.keys(item)
      .sort()
      .map((k) => {
        const v = item[k];
        if (isObject(v)) {
          return [k, JSON.stringify(v)] as [string, unknown];
        }
        return [k, v] as [string, unknown];
      });

    pairs = [...pairs, ...itemPairs];
  });

  return pairs;
}

function signatureString(
  pairs: Array<[string, unknown]>,
  doSort = true
): string {
  // 过滤掉值为null或undefined的键值对
  let filteredPairs = pairs.filter(([, v]) => !isNil(v));

  // 按键名排序
  if (doSort) {
    filteredPairs = sort(filteredPairs, ([k]) => k as unknown as number);
  }

  // 转换为字符串
  return filteredPairs.map(([k, v]) => `${k}=${v}`).join('');
}

function signature(
  salt: string | undefined,
  query: Record<string, unknown>,
  body: unknown
): string {
  const encrypted = (salt_: string | undefined, raw: string): string => {
    return md5(`${raw}${salt_ || ''}`);
  };

  const pq = pairsQuery(query);
  const pbg = pairsBodyGeneral(body);
  const pba = pairsBodyArray(body);
  const s1 = signatureString([...pq, ...pbg]);
  const s2 = signatureString(pba, false);
  const es = encrypted(salt, `${s1}${s2}`);
  return es;
}

type SignatureInterceptorOptions = {
  salt?: string;
};

export function interceptorSignature({
  salt,
}: SignatureInterceptorOptions): (
  config: InternalAxiosRequestConfig
) => InternalAxiosRequestConfig {
  return (config: InternalAxiosRequestConfig) => {
    const s = signature(salt, config.params || {}, config.data);

    config.params = {
      ...config.params,
      signature: s,
    };

    return config;
  };
}

import axios from 'axios';
import type { AxiosInstance, ResponseType, AxiosProgressEvent } from 'axios';
import qs from 'qs';
import jsonBigInt from 'json-bigint';

import { interceptorAjax } from './interceptors/ajax';
import { interceptorSignature } from './interceptors/signature';
import { interceptorNilFilter } from './interceptors/nil-filter';
import { interceptorNonce } from './interceptors/nonce';
import { interceptorLocale } from './interceptors/locale';
import { interceptorError } from './interceptors/error';

type CreateRequestOptions = {
  baseURL?: string;
  timeout?: number;
  responseType?: ResponseType;
  headers?: Record<string, string>;
  onUploadProgress?: (progressEvent: AxiosProgressEvent) => void;
  onDownloadProgress?: (progressEvent: AxiosProgressEvent) => void;
  salt?: string;
  localeCookieKey?: string;
};

export function createRequest({
  baseURL,
  timeout,
  responseType,
  headers,
  onUploadProgress,
  onDownloadProgress,
  salt,
}: CreateRequestOptions): AxiosInstance {
  const xhr = axios.create({
    baseURL,
    headers,
    responseType,
    timeout: Number.isFinite(timeout) ? timeout : 20 * 1000, // 默认超时时间为20s
    paramsSerializer: {
      serialize(params) {
        return qs.stringify(params, { indices: false });
      },
    },
    adapter: 'fetch',
    maxRedirects: 0,
    transformResponse: [
      (data) => {
        return jsonBigInt({ storeAsString: true }).parse(data);
      },
    ],
    onUploadProgress,
    onDownloadProgress,
  });

  xhr.interceptors.request.use(interceptorAjax);
  xhr.interceptors.request.use(interceptorSignature({ salt }));
  xhr.interceptors.request.use(interceptorNilFilter);
  xhr.interceptors.request.use(interceptorNonce);
  xhr.interceptors.request.use(interceptorLocale());

  xhr.interceptors.response.use(interceptorError);

  return xhr;
}

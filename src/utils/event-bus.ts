import mitt from 'mitt';

const emitter = mitt<MittEvents>();

function on<K extends keyof MittEvents>(
  event: K,
  fn: (data?: MittEvents[K]) => void
) {
  emitter.on(event, fn);
  return () => emitter.off(event, fn);
}

function emit<K extends keyof MittEvents>(event: K, data?: MittEvents[K]) {
  emitter.emit(event, data as MittEvents[K]);
}

function clearAll() {
  emitter.all.clear();
}

export default {
  on,
  emit,
  clearAll,
};

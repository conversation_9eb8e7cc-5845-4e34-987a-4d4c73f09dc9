import { createApp } from '@/main';

const piniaState = window.__PINIA_STATE__;
const locale = piniaState.i18n.locale;
const appLoggedIn = piniaState.loginStatus.appLoggedIn;
const userCategory = piniaState.userCategory.userCategory;

const { app, router, pinia } = createApp({ locale, appLoggedIn, userCategory });

pinia.state.value = piniaState;

// 初始化完成后挂载应用
router.isReady().then(() => {
  app.mount('#app');
});

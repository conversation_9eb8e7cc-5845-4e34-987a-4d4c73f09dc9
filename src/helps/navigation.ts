import { useLocation, useNavigate } from 'react-router-dom';

import { PUBLIC_PATH } from '@/config/router';

/**
 * 判断是否为根路由
 * React Hook版本
 */
export function useIsRootRoute() {
  const location = useLocation();
  return location.pathname === PUBLIC_PATH;
}

/**
 * 跳转到第一个路由
 * React Hook版本
 */
export function useGoFirstRoute() {
  const navigate = useNavigate();

  return () => {
    // 在React Router中，我们直接导航到home路由
    navigate(`${PUBLIC_PATH}/home`);
  };
}

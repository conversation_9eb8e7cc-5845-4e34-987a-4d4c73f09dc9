import type { Router } from 'vue-router';

import { RouterName } from '@/config/router';

/**
 * 判断是否为根路由
 *
 * @param router
 * @returns
 */
export function isRootRoute(router: Router) {
  const currentRoute = router.currentRoute.value;
  return currentRoute.name === RouterName.Root;
}

/**
 * 跳转到第一个路由
 *
 * @param router
 */
export function goFirstRoute(router: Router) {
  const firstRoute = router.getRoutes().find((r) => r.name !== RouterName.Root);
  if (firstRoute) {
    router.replace(firstRoute.path);
  }
}

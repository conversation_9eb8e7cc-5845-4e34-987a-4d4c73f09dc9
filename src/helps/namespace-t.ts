import { i18n } from '@/i18n';
import type enUS from '@/i18n/en-US';

export type MessageSchema = typeof enUS;

/**
 * 从嵌套对象类型中提取所有可能的键路径
 * 例如: { a: { b: { c: string } } } => 'a' | 'a.b' | 'a.b.c'
 */
export type NestedKeyOf<ObjectType extends object> = {
  [Key in keyof ObjectType & (string | number)]: ObjectType[Key] extends object
    ? `${Key}` | `${Key}.${NestedKeyOf<ObjectType[Key]>}`
    : `${Key}`;
}[keyof ObjectType & (string | number)];

/**
 * 从嵌套对象类型中提取指定前缀下的键路径
 * 例如: GetNestedKeys<MessageSchema, 'home'> => 'text.welcome' | 'placeholder.input'
 */
export type GetNestedKeys<
  T extends object,
  Prefix extends string,
> = Prefix extends keyof T
  ? T[Prefix] extends object
    ? NestedKeyOf<T[Prefix]>
    : never
  : never;

/**
 * 所有可用的翻译键路径
 */
export type TranslationKey = NestedKeyOf<MessageSchema>;

/**
 * 命名空间翻译键类型
 */
export type NamespaceTranslationKey<T extends string> =
  T extends keyof MessageSchema
    ? GetNestedKeys<MessageSchema, T>
    : TranslationKey;

/**
 * 创建带命名空间的翻译函数
 * TS函数重载
 *
 * @param path - 命名空间路径，如 'home', 'consts' 等
 * @returns 翻译函数，支持类型提示和自动补全
 *
 * @example
 * ```typescript
 * // 不带命名空间，使用完整路径
 * const t = namespaceT();
 * t('home.text.welcome'); // 有类型提示
 *
 * // 带命名空间，使用相对路径
 * const t = namespaceT('home');
 * t('text.welcome'); // 有类型提示
 * t('placeholder.input'); // 有类型提示
 * ```
 */
export function namespaceT(): (key: TranslationKey) => string;
export function namespaceT<T extends keyof MessageSchema>(
  path: T
): (key: NamespaceTranslationKey<T>) => string;

export function namespaceT<T extends keyof MessageSchema>(
  path?: T
):
  | ((key: TranslationKey) => string)
  | ((key: NamespaceTranslationKey<T>) => string) {
  if (!path) {
    return (key: TranslationKey) => i18n.global.t(key);
  }

  return (key: NamespaceTranslationKey<T>) =>
    i18n.global.t(`${path}.${key}` as TranslationKey);
}

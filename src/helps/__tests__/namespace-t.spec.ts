import { describe, expect, it, beforeEach } from 'vitest';
import { namespaceT } from '../namespace-t';
import { i18n } from '@/i18n';
import { Locale } from '@/config/locale';

describe('namespaceT', () => {
  beforeEach(() => {
    // 确保使用中文语言环境进行测试
    i18n.global.locale.value = Locale.ZH_MO;
  });

  describe('不带命名空间的使用', () => {
    it('应该返回完整路径的翻译', () => {
      const t = namespaceT();

      // 测试完整路径翻译
      expect(t('title')).toBe('學術資源統一檢索');
      expect(t('home.title')).toBe('你好，欢迎来到學術資源統一檢索！');
      expect(t('home.placeholder.input')).toBe('你想找些什麼？');
    });

    it('应该处理不存在的键', () => {
      const t = namespaceT();

      // 测试不存在的键，应该返回键本身
      // @ts-expect-error 测试运行时错误处理
      expect(t('nonexistent.key')).toBe('nonexistent.key');
    });
  });

  describe('带命名空间的使用', () => {
    it('应该返回命名空间下的翻译', () => {
      const t = namespaceT('home');

      // 测试命名空间下的相对路径翻译
      expect(t('title')).toBe('你好，欢迎来到學術資源統一檢索！');
      expect(t('placeholder.input')).toBe('你想找些什麼？');
    });

    it('应该处理 consts 命名空间', () => {
      const t = namespaceT('consts');

      // 测试 consts 命名空间
      expect(t('displayMode.CHAT')).toBe('聊天模式');
      expect(t('displayMode.LIST')).toBe('列表模式');
    });

    it('应该处理不存在的命名空间键', () => {
      const t = namespaceT('home');

      // 测试不存在的键，应该返回完整路径
      // @ts-expect-error 测试运行时错误处理
      expect(t('nonexistent.key')).toBe('home.nonexistent.key');
    });
  });

  describe('类型安全性', () => {
    it('应该提供正确的类型推断', () => {
      // 这些测试主要是为了确保 TypeScript 编译通过
      // 实际的类型检查在编译时进行

      const globalT = namespaceT();
      const homeT = namespaceT('home');
      const constsT = namespaceT('consts');

      // 这些调用应该在编译时通过类型检查
      expect(typeof globalT('title')).toBe('string');
      expect(typeof homeT('title')).toBe('string');
      expect(typeof constsT('displayMode.CHAT')).toBe('string');
    });
  });

  describe('语言切换', () => {
    it('应该根据当前语言返回对应翻译', () => {
      const t = namespaceT('home');

      // 切换到英文
      i18n.global.locale.value = Locale.EN_US;
      expect(t('title')).toBe(
        'Hello, welcome to Academic Resource Unified Search!'
      );

      // 切换回中文
      i18n.global.locale.value = Locale.ZH_MO;
      expect(t('title')).toBe('你好，欢迎来到學術資源統一檢索！');
    });
  });
});

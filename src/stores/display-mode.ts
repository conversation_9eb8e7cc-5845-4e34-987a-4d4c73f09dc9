import { defineStore } from 'pinia';
import { ref, computed } from 'vue';
import { DisplayMode } from '@/consts/display-mode';
import { useI18n } from 'vue-i18n';

export const useDisplayModeStore = defineStore('displayMode', () => {
  const displayMode = ref<DisplayMode>(DisplayMode.CHAT);

  // 使用 computed 来确保 i18n 在需要时才被调用
  const list = computed(() => {
    const { t } = useI18n();
    return Object.values(DisplayMode).map((v) => ({
      label: t(`consts.displayMode.${v}`),
      value: v,
    }));
  });

  return {
    displayMode,
    list,
  };
});

import { create } from 'zustand';
import { DisplayMode } from '@/consts/display-mode';

interface DisplayModeStore {
  displayMode: DisplayMode;
  setDisplayMode: (displayMode: DisplayMode) => void;
  getList: () => Array<{ label: string; value: DisplayMode }>;
}

export const useDisplayModeStore = create<DisplayModeStore>((set) => ({
  displayMode: DisplayMode.CHAT,
  setDisplayMode: (displayMode) => set({ displayMode }),
  getList: () => {
    // 注意：这里需要在组件中使用 useTranslation hook
    // 这个方法主要用于获取选项结构，实际翻译在组件中处理
    return Object.values(DisplayMode).map((v) => ({
      label: `consts.displayMode.${v}`, // 返回翻译键，在组件中使用t()函数翻译
      value: v,
    }));
  },
}));

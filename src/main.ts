import { createSSRApp } from 'vue';
import { createPinia } from 'pinia';
import type { Locale } from 'vue-i18n';

import App from '@/app.vue';
import { SUPPORT_LOCALES, FALLBACK_LOCALE } from '@/config/locale';
import { createRouter } from '@/router';
import { i18n } from '@/i18n';
import { useI18nStore } from '@/stores/i18n';
import { useLoginStatusStore } from '@/stores/login-status';
import { useUserCategoryStore } from '@/stores/user-category';
import type { UserCategory } from '@/config/user-category';

import '@/styles/app.css';

interface CreateAppOptions {
  locale: Locale;
  appLoggedIn: boolean;
  userCategory: UserCategory;
}

export function createApp({
  locale,
  appLoggedIn,
  userCategory,
}: CreateAppOptions) {
  const app = createSSRApp(App);
  const pinia = createPinia();
  const router = createRouter();

  // 先初始化pinia
  app.use(i18n);
  app.use(pinia);

  // 设置i18n的locale
  i18n.global.locale.value = SUPPORT_LOCALES.includes(locale)
    ? locale
    : FALLBACK_LOCALE;

  // 设置locale到store中
  const i18nStore = useI18nStore();
  i18nStore.locale = i18n.global.locale.value;

  // 设置用户分类到store中
  const userCategoryStore = useUserCategoryStore();
  userCategoryStore.userCategory = userCategory;

  // 设置登录状态到store中
  const loginStatusStore = useLoginStatusStore();
  loginStatusStore.appLoggedIn = appLoggedIn;

  app.use(router);

  return { app, router, i18n, pinia };
}

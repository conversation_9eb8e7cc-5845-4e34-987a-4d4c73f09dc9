import { renderToString } from 'react-dom/server';
import { RouterProvider } from 'react-router-dom';
import { I18nextProvider } from 'react-i18next';
type Locale = string;
import { createApp } from '@/main';
import type { UserCategory } from '@/config/user-category';
import { BaseError } from '@/errors/base-error';

interface RenderOptions {
  url: string;
  locale: Locale;
  appLoggedIn: boolean;
  userCategory: UserCategory;
}

export async function render({
  url,
  locale,
  appLoggedIn,
  userCategory,
}: RenderOptions) {
  const { router, i18n } = createApp({
    locale,
    appLoggedIn,
    userCategory,
    isSSR: true,
    url,
  });

  // 检查路由认证 - 根据路由配置来判断是否需要认证
  const urlObj = new URL(url, 'http://localhost');
  const hasTicket = urlObj.searchParams.has('ticket');

  // 如果没有ticket且用户未登录，抛出LOGIN_REQUIRED错误
  // 这将被错误处理中间件捕获并触发CAS登录
  if (!hasTicket && appLoggedIn === false) {
    const err = new BaseError('Login required');
    err.code = 'LOGIN_REQUIRED';
    throw err;
  }

  // 确保i18n已经完全初始化
  await new Promise<void>((resolve) => {
    if (i18n.isInitialized) {
      resolve();
    } else {
      i18n.on('initialized', () => resolve());
    }
  });

  const html = renderToString(
    <I18nextProvider i18n={i18n}>
      <RouterProvider router={router} />
    </I18nextProvider>
  );

  // 生成初始状态用于客户端水合
  const initialState = {
    locale,
    appLoggedIn,
    userCategory,
  };

  return {
    html,
    initialState,
    head: `<title>${i18n.t('title')}</title>`,
  };
}

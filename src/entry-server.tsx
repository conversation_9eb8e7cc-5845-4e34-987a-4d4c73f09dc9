import React from 'react';
import { renderToString } from 'react-dom/server';
import { StaticRouter } from 'react-router-dom/server';
import { I18nextProvider } from 'react-i18next';
type Locale = string;
import { createApp } from '@/main';
import type { UserCategory } from '@/config/user-category';
import { BaseError } from '@/errors/base-error';

interface RenderOptions {
  url: string;
  locale: Locale;
  appLoggedIn: boolean;
  userCategory: UserCategory;
}

export async function render({
  url,
  locale,
  appLoggedIn,
  userCategory,
}: RenderOptions) {
  const { app, i18n } = createApp({
    locale,
    appLoggedIn,
    userCategory,
    isSSR: true,
    url,
  });

  // 检查路由认证 - 这里需要根据路由配置来判断
  // 简化版本，实际需要根据路由匹配来判断
  const urlObj = new URL(url, 'http://localhost');
  const hasTicket = urlObj.searchParams.has('ticket');

  // 如果没有ticket且需要认证且用户未登录，抛出错误
  if (!hasTicket && appLoggedIn === false) {
    // 这里可以根据具体路由来判断是否需要认证
    // 暂时简化处理
    const err = new BaseError('Login required');
    err.code = 'LOGIN_REQUIRED';
    throw err;
  }

  const html = renderToString(
    <I18nextProvider i18n={i18n}>
      <StaticRouter location={url}>{app}</StaticRouter>
    </I18nextProvider>
  );

  // 生成初始状态用于客户端水合
  const initialState = {
    locale,
    appLoggedIn,
    userCategory,
  };

  return {
    html,
    initialState,
    head: `<title>${i18n.t('title')}</title>`,
  };
}

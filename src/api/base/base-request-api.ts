import type {
  ResponseType,
  RawAxiosRequestHeaders,
  AxiosResponse,
  AxiosInstance,
  Method,
} from 'axios';

import { createRequest } from '@/utils/request';
import { errorInterceptor } from './error-interceptor';

export class BaseRequestApi {
  baseURL!: string;

  responseType!: ResponseType;

  headers!: RawAxiosRequestHeaders;

  salt!: string;

  targetParams: { [key: string]: unknown } | Array<unknown>;

  targetData: { [key: string]: unknown } | Array<unknown> | FormData;

  request: AxiosInstance;

  constructor(args: CreateRequestApiOptions) {
    let baseURL;
    if ('baseURL' in args) {
      ({ baseURL } = args);
    }

    let timeout;
    if ('timeout' in args) {
      ({ timeout } = args);
    }

    let responseType;
    if ('responseType' in args) {
      ({ responseType } = args);
    }

    let headers;
    if ('headers' in args) {
      ({ headers } = args);
    }

    let salt;
    if ('salt' in args) {
      ({ salt } = args);
    }

    this.request = createRequest({
      baseURL,
      timeout,
      responseType,
      headers,
      salt,
    });
    this.request.interceptors.response.use(
      (response: AxiosResponse) => response,
      (error: ApiError) => errorInterceptor(error)
    );

    this.targetParams = this.defaultParams();
    this.targetData = this.defaultData() as
      | { [key: string]: unknown }
      | Array<unknown>
      | FormData;
  }

  method(): Method {
    return 'GET';
  }

  url(): string | undefined {
    return undefined;
  }

  defaultParams() {
    return {};
  }

  get params(): { [key: string]: unknown } | Array<unknown> {
    return this.targetParams;
  }

  set params(value: { [key: string]: unknown } | Array<unknown>) {
    this.targetParams = {
      ...this.defaultParams(),
      ...value,
    };
  }

  defaultData(): object | Array<unknown> {
    return {};
  }

  get data() {
    return this.targetData;
  }

  set data(value: { [key: string]: unknown } | Array<unknown> | FormData) {
    if (value instanceof FormData) {
      this.targetData = value;
      return;
    }

    if (Array.isArray(value)) {
      let defaultData = this.defaultData();
      defaultData = Array.isArray(defaultData) ? defaultData : [defaultData];
      this.targetData = [...(defaultData as Array<unknown>), ...value];
      return;
    }

    this.targetData = {
      ...this.defaultData(),
      ...value,
    };
  }

  send() {
    const method = this.method().toUpperCase();
    const isGetOrHeadMethod = ['GET', 'HEAD'].includes(method);
    const data = isGetOrHeadMethod ? undefined : this.targetData;

    return this.request({
      method,
      data,
      url: this.url(),
      params: this.targetParams,
    });
  }
}

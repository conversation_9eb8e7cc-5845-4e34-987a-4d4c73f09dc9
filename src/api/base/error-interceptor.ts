import emitter from '@/utils/event-bus';
import { i18n } from '@/i18n';

export function errorInterceptor(error: ApiError) {
  // 在React中，我们直接使用i18n实例的t方法
  const t = (key: string) => i18n.t(key);

  // 如果响应为401状态码错误，则通知相关事件
  if (error.response?.status === 401) {
    emitter.emit('userUnauthorizedError');
    throw error;
  }

  // 用户AccessToken失效
  if (
    [
      'F_GET_TOKEN_FAILED',
      'ACCESS_CHECK',
      'AUTHORIZATION_NULL_AND_VOID',
    ].includes(error.code)
  ) {
    emitter.emit('invalidAccessTokenError');
    throw error;
  }

  // 用户RefreshToken失效
  if (
    error.code === 'F_REFRESH_TOKEN_FAILED' ||
    (error.code === 'INVALID' &&
      error.response?.data?.errorList?.INVALID === 'refreshToken')
  ) {
    emitter.emit('invalidRefreshTokenError');
    throw error;
  }

  if (
    [
      'ACCOUNT_DISABLE',
      'ACCOUNT_FREEZE_OR_DEACTIVATE',
      'ACCOUNT_EXCEPTION',
    ].includes(error.code)
  ) {
    emitter.emit('catchAccountError', error.code);
    throw error;
  }

  // 超时
  if (
    error.response?.status === 408 ||
    (error.code === 'ECONNABORTED' &&
      /timeout of \d+ms exceeded$/.test(error.message))
  ) {
    const err: ApiError = new Error(t('common.error.timeoutError')) as ApiError;
    err.code = error.code;
    err.stack = error.stack;
    throw err;
  }

  // 未知错误
  if (error.code === 'OTHER') {
    const err = new Error(t('common.error.unknownError')) as ApiError;
    err.code = error.code;
    err.stack = error.stack;
    throw err;
  }

  // Network Error 由浏览器抛出 （chrome | safari | edge | firefox 测试可用）
  if (error.message === 'Network Error') {
    throw new Error(t('common.error.networkError'));
  }

  throw error;
}

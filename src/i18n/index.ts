import i18n from 'i18next';
import { initReactI18next } from 'react-i18next';
import { Locale } from '@/config/locale';
import enUS from './en-US';
import zhMO from './zh-MO';

interface InitI18nOptions {
  lng?: string;
}

// 全局i18n实例，确保服务端和客户端使用同一个实例
let globalI18n: typeof i18n | null = null;

export function initI18n(options: InitI18nOptions = {}) {
  const { lng = Locale.ZH_MO } = options;

  // 如果已经初始化过，只更新语言
  if (globalI18n && globalI18n.isInitialized) {
    if (globalI18n.language !== lng) {
      globalI18n.changeLanguage(lng);
    }
    return globalI18n;
  }

  // 创建新的i18n实例
  const i18nInstance = i18n.createInstance();

  i18nInstance.use(initReactI18next).init({
    lng,
    fallbackLng: Locale.ZH_MO,
    interpolation: {
      escapeValue: false, // React已经默认转义
    },
    resources: {
      [Locale.ZH_MO]: { translation: zhMO },
      [Locale.EN_US]: { translation: enUS },
    },
    // 确保SSR和客户端渲染一致
    react: {
      useSuspense: false, // 禁用Suspense以避免SSR问题
    },
  });

  globalI18n = i18nInstance;
  return i18nInstance;
}

export { i18n };

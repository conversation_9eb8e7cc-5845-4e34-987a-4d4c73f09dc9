import i18n from 'i18next';
import { initReactI18next } from 'react-i18next';
import { Locale } from '@/config/locale';
import enUS from './en-US';
import zhMO from './zh-MO';

interface InitI18nOptions {
  lng?: string;
}

export function initI18n(options: InitI18nOptions = {}) {
  const { lng = Locale.ZH_MO } = options;

  i18n.use(initReactI18next).init({
    lng,
    fallbackLng: Locale.ZH_MO,
    interpolation: {
      escapeValue: false, // React已经默认转义
    },
    resources: {
      [Locale.ZH_MO]: { translation: zhMO },
      [Locale.EN_US]: { translation: enUS },
    },
  });

  return i18n;
}

export { i18n };

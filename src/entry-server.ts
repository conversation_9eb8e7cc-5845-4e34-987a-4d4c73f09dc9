import { renderToString } from 'vue/server-renderer';
import type { Locale } from 'vue-i18n';
import { setup } from '@css-render/vue3-ssr';

import { UserCategory } from '@/config/user-category';
import { PUBLIC_PATH } from '@/config/public-path';
import { createApp } from '@/main';
import { BaseError } from '@/errors/base-error';

interface RenderOptions {
  url: string;
  locale: Locale;
  appLoggedIn: boolean;
  userCategory: UserCategory;
}

// Favicon文件地址
function getFaviconUrl(userCategory: string) {
  if (userCategory === UserCategory.STUDENT) {
    return `${PUBLIC_PATH}student-favicon.ico`;
  }

  return `${PUBLIC_PATH}favicon.ico`;
}

export async function render({
  url,
  locale,
  appLoggedIn,
  userCategory,
}: RenderOptions) {
  const { app, router, pinia, i18n } = createApp({
    locale,
    appLoggedIn,
    userCategory,
  });

  router.push(url);
  await router.isReady();

  // 如果在路由配置里 meta.requiresAuth 为 false，则为当前路由可不通过认证访问，反之亦然
  // meta.requiresAuth 不配置或配置为 true，则为需要认证
  // 当前用户的认证状态保存于 ctx.appLoggedIn，该值由server端维护
  // 当当前访问地址带有 ticket 时，需要进行放行，该参数为 CAS 认证成功后返回值
  if (
    !router.currentRoute.value.query?.ticket &&
    router.currentRoute.value.meta?.requiresAuth !== false &&
    appLoggedIn === false
  ) {
    const err = new BaseError('Login required');
    err.code = 'LOGIN_REQUIRED';
    throw err;
  }

  const { collect } = setup(app);
  const html = await renderToString(app);
  const cssHtml = collect();

  // 添加语言标签到head中
  const head = `
    <link rel="icon" type="image/x-icon" href="${getFaviconUrl(userCategory)}">
    <title>${i18n.global.t('title')}</title>
    ${cssHtml}
    <script>window.__PINIA_STATE__=${JSON.stringify(pinia.state.value)}</script>
  `;

  return { html, head };
}

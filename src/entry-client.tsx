import React from 'react';
import { hydrateRoot } from 'react-dom/client';
import { RouterProvider } from 'react-router-dom';
import { I18nextProvider } from 'react-i18next';
import { createApp } from '@/main';
import type { UserCategory } from '@/config/user-category';

// 从服务器端获取初始状态
declare global {
  interface Window {
    __INITIAL_STATE__: {
      locale: string;
      appLoggedIn: boolean;
      userCategory: string;
    };
  }
}

const initialState = window.__INITIAL_STATE__;
const locale = initialState.locale;
const appLoggedIn = initialState.appLoggedIn;
const userCategory = initialState.userCategory as UserCategory;

const { router, i18n } = createApp({
  locale,
  appLoggedIn,
  userCategory,
  isSSR: false,
});

const container = document.getElementById('app');

if (container) {
  hydrateRoot(
    container,
    <React.StrictMode>
      <I18nextProvider i18n={i18n}>
        <RouterProvider router={router} />
      </I18nextProvider>
    </React.StrictMode>
  );
}

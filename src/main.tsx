import { createBrowserRouter, createMemoryRouter } from 'react-router-dom';
type Locale = string;

import { SUPPORT_LOCALES, FALLBACK_LOCALE } from '@/config/locale';
import { routes } from '@/router/routes';
import { initI18n } from '@/i18n';
import { useI18nStore } from '@/stores/i18n';
import { useLoginStatusStore } from '@/stores/login-status';
import { useUserCategoryStore } from '@/stores/user-category';
import type { UserCategory } from '@/config/user-category';

import '@/styles/app.css';

interface CreateAppOptions {
  locale: Locale;
  appLoggedIn: boolean;
  userCategory: UserCategory;
  isSSR?: boolean;
  url?: string;
}

export function createApp({
  locale,
  appLoggedIn,
  userCategory,
  isSSR = false,
  url = '/',
}: CreateAppOptions) {
  // 初始化i18n
  const i18n = initI18n({
    lng: SUPPORT_LOCALES.includes(locale) ? locale : FALLBACK_LOCALE,
  });

  // 初始化stores
  const i18nStore = useI18nStore.getState();
  const loginStatusStore = useLoginStatusStore.getState();
  const userCategoryStore = useUserCategoryStore.getState();

  // 设置初始状态
  i18nStore.setLocale(i18n.language as Locale);
  loginStatusStore.setAppLoggedIn(appLoggedIn);
  userCategoryStore.setUserCategory(userCategory);

  // 创建路由器
  const router = isSSR
    ? createMemoryRouter(routes, { initialEntries: [url || '/'] })
    : createBrowserRouter(routes);

  return {
    router,
    i18n,
    stores: {
      i18n: i18nStore,
      loginStatus: loginStatusStore,
      userCategory: userCategoryStore,
    },
  };
}

import React from 'react';
import { useTranslation } from 'react-i18next';

const Home: React.FC = () => {
  const { t } = useTranslation();

  return (
    <div className="min-h-screen bg-gray-50 flex items-center justify-center">
      <div className="text-center">
        <h1 className="text-4xl font-bold text-gray-900 mb-4">
          {t('home.title')}
        </h1>
        <div className="max-w-md mx-auto">
          <input
            type="text"
            placeholder={t('home.placeholder.input')}
            className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
          />
        </div>
        <div className="mt-8 text-sm text-gray-600">
          {t('home.footer.resource')}
        </div>
      </div>
    </div>
  );
};

export default Home;

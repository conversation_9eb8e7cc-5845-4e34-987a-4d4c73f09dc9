{"extends": ["@tsconfig/node22/tsconfig.json"], "include": ["vite.config.*", "vitest.config.*", "cypress.config.*", "nightwatch.conf.*", "playwright.config.*", "eslint.config.*", "server/**/*"], "compilerOptions": {"strict": true, "noEmit": true, "tsBuildInfoFile": "./node_modules/.tmp/tsconfig.node.tsbuildinfo", "module": "ESNext", "moduleResolution": "<PERSON><PERSON><PERSON>", "types": ["node"], "typeRoots": ["./node_modules/@types", "./@types"]}}
{"name": "wm-arus-nodejs", "version": "0.0.0", "private": true, "type": "module", "scripts": {"dev": "tsx server/server.ts", "build": "run-s build:*", "build:client": "vite build --mode production", "build:server": "vite build --mode production --ssr", "preview": "cross-env NODE_ENV=production tsx server/server.ts", "test:unit": "vitest run", "test:unit:watch": "vitest", "test:e2e": "playwright test", "type-check": "tsc --build", "lint:oxlint": "oxlint . --fix -D correctness --ignore-path .gitignore", "lint:eslint": "eslint . --fix", "lint:style": "stylelint \"**/*.{css,tsx}\"", "lint:style:fix": "stylelint \"**/*.{css,tsx}\" --fix", "lint": "run-s lint:*", "format": "prettier --write src/ server/", "lefthook": "lefthook install", "commit": "git add -A && git-cz && git push"}, "dependencies": {"@mdx-js/mdx": "^3.1.0", "@tailwindcss/vite": "^4.1.6", "@tsconfig/node22": "^22.0.1", "@types/react": "^18.3.17", "@types/react-dom": "^18.3.5", "@vitejs/plugin-react": "^4.3.4", "axios": "^1.9.0", "cas-authentication": "^0.0.8", "compression": "^1.8.0", "connect-redis": "^8.1.0", "cookie-parser": "^1.4.7", "cross-env": "^7.0.3", "dotenv": "^16.5.0", "express": "^5.1.0", "express-session": "^1.18.1", "file-saver": "^2.0.5", "helmet": "^8.1.0", "i18next": "^23.15.2", "ioredis": "^5.6.1", "js-md5": "^0.8.3", "json-bigint": "^1.0.0", "mitt": "^3.0.1", "nanoid": "^5.1.5", "qs": "^6.14.0", "radash": "^12.1.0", "react": "^18.3.1", "react-dom": "^18.3.1", "react-i18next": "^15.1.1", "react-router-dom": "^6.28.0", "redlock": "5.0.0-beta.2", "sirv": "^3.0.1", "tailwindcss": "^4.1.6", "terser": "^5.39.2", "tsx": "^4.19.4", "urijs": "^1.19.11", "vite": "^6.2.4", "xml2js": "^0.6.2", "zustand": "^5.0.2"}, "devDependencies": {"@commitlint/cli": "^19.8.1", "@commitlint/config-conventional": "^19.8.1", "@eslint/js": "^9.29.0", "@playwright/test": "^1.51.1", "@testing-library/jest-dom": "^6.6.3", "@testing-library/react": "^16.1.0", "@types/compression": "^1.7.5", "@types/cookie-parser": "^1.4.8", "@types/express": "^5.0.1", "@types/express-session": "^1.18.1", "@types/file-saver": "^2.0.7", "@types/js-md5": "^0.7.2", "@types/jsdom": "^21.1.7", "@types/json-bigint": "^1.0.4", "@types/node": "^22.14.0", "@types/qs": "^6.14.0", "@types/redlock": "^4.0.7", "@types/urijs": "^1.19.25", "@types/xml2js": "^0.4.14", "@typescript-eslint/eslint-plugin": "^8.21.0", "@typescript-eslint/parser": "^8.21.0", "@vitest/eslint-plugin": "^1.1.39", "commitizen": "^4.3.1", "cz-conventional-changelog": "^3.3.0", "eslint": "^9.22.0", "eslint-plugin-oxlint": "^0.16.0", "eslint-plugin-playwright": "^2.2.0", "eslint-plugin-react": "^7.37.2", "eslint-plugin-react-hooks": "^5.1.0", "jiti": "^2.4.2", "jsdom": "^26.0.0", "lefthook": "^1.11.12", "npm-run-all2": "^7.0.2", "oxlint": "^0.16.0", "postcss-html": "^1.8.0", "prettier": "3.5.3", "prettier-plugin-tailwindcss": "^0.6.11", "stylelint": "^16.19.1", "stylelint-config-standard": "^38.0.0", "stylelint-config-standard-scss": "^13.1.0", "stylelint-order": "^7.0.0", "typescript": "~5.8.0", "typescript-eslint": "^8.35.0", "vitest": "^3.1.1"}, "engines": {"node": ">=22.0.0"}, "config": {"commitizen": {"path": "cz-conventional-changelog"}}}
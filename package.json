{"name": "wm-arus-nodejs", "version": "0.0.0", "private": true, "type": "module", "scripts": {"dev": "tsx server/server.ts", "build": "run-s build:*", "build:client": "vite build --mode production", "build:server": "vite build --mode production --ssr", "preview": "cross-env NODE_ENV=production tsx server/server.ts", "test:unit": "vitest run", "test:unit:watch": "vitest", "test:e2e": "playwright test", "type-check": "vue-tsc --build", "lint:oxlint": "oxlint . --fix -D correctness --ignore-path .gitignore", "lint:eslint": "eslint . --fix", "lint:style": "stylelint \"**/*.{css,vue}\"", "lint:style:fix": "stylelint \"**/*.{css,vue}\" --fix", "lint": "run-s lint:*", "format": "prettier --write src/ server/", "lefthook": "lefthook install", "commit": "git add -A && git-cz && git push"}, "dependencies": {"@css-render/vue3-ssr": "^0.15.14", "@mdx-js/mdx": "^3.1.0", "@tailwindcss/vite": "^4.1.6", "@tsconfig/node22": "^22.0.1", "@vitejs/plugin-vue": "^5.2.4", "@vitejs/plugin-vue-jsx": "^4.1.2", "@vue/tsconfig": "^0.7.0", "axios": "^1.9.0", "cas-authentication": "^0.0.8", "compression": "^1.8.0", "connect-redis": "^8.1.0", "cookie-parser": "^1.4.7", "cross-env": "^7.0.3", "dotenv": "^16.5.0", "express": "^5.1.0", "express-session": "^1.18.1", "file-saver": "^2.0.5", "helmet": "^8.1.0", "ioredis": "^5.6.1", "js-md5": "^0.8.3", "json-bigint": "^1.0.0", "mitt": "^3.0.1", "nanoid": "^5.1.5", "pinia": "^3.0.1", "qs": "^6.14.0", "radash": "^12.1.0", "redlock": "5.0.0-beta.2", "sirv": "^3.0.1", "tailwindcss": "^4.1.6", "terser": "^5.39.2", "tsx": "^4.19.4", "urijs": "^1.19.11", "vite": "^6.2.4", "vue": "^3.5.13", "vue-i18n": "^11.1.3", "vue-router": "^4.5.0", "xml2js": "^0.6.2"}, "devDependencies": {"@commitlint/cli": "^19.8.1", "@commitlint/config-conventional": "^19.8.1", "@playwright/test": "^1.51.1", "@types/compression": "^1.7.5", "@types/cookie-parser": "^1.4.8", "@types/express": "^5.0.1", "@types/express-session": "^1.18.1", "@types/file-saver": "^2.0.7", "@types/js-md5": "^0.7.2", "@types/jsdom": "^21.1.7", "@types/json-bigint": "^1.0.4", "@types/node": "^22.14.0", "@types/qs": "^6.14.0", "@types/redlock": "^4.0.7", "@types/urijs": "^1.19.25", "@types/xml2js": "^0.4.14", "@vitest/eslint-plugin": "^1.1.39", "@vue/eslint-config-prettier": "^10.2.0", "@vue/eslint-config-typescript": "^14.5.0", "@vue/test-utils": "^2.4.6", "commitizen": "^4.3.1", "cz-conventional-changelog": "^3.3.0", "eslint": "^9.22.0", "eslint-plugin-oxlint": "^0.16.0", "eslint-plugin-playwright": "^2.2.0", "eslint-plugin-vue": "~10.0.0", "jiti": "^2.4.2", "jsdom": "^26.0.0", "lefthook": "^1.11.12", "npm-run-all2": "^7.0.2", "oxlint": "^0.16.0", "postcss-html": "^1.8.0", "prettier": "3.5.3", "prettier-plugin-tailwindcss": "^0.6.11", "stylelint": "^16.19.1", "stylelint-config-recommended-vue": "^1.6.0", "stylelint-config-standard": "^38.0.0", "stylelint-order": "^7.0.0", "typescript": "~5.8.0", "vite-plugin-vue-devtools": "^7.7.2", "vitest": "^3.1.1", "vue-tsc": "^2.2.8"}, "engines": {"node": ">=22.0.0"}, "config": {"commitizen": {"path": "cz-conventional-changelog"}}}
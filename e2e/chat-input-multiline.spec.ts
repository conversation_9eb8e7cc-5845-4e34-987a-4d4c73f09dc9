import { test, expect } from '@playwright/test';

test.describe('ChatInput Multiline 功能测试', () => {
  test.beforeEach(async ({ page }) => {
    await page.goto('/arus/home');

    // 等待页面加载完成
    await expect(page.getByTestId('multiline-input')).toBeVisible();
    await expect(page.getByTestId('singleline-input')).toBeVisible();

    // 清理消息状态 - 重新加载页面确保干净的状态
    await page.reload();
    await expect(page.getByTestId('multiline-input')).toBeVisible();
    await expect(page.getByTestId('singleline-input')).toBeVisible();
  });

  test.describe('多行模式 (multiline: true)', () => {
    test('应该支持 Enter 发送消息', async ({ page }) => {
      const multilineInput = page
        .getByTestId('multiline-input')
        .locator('textarea');
      const messagesContainer = page.getByTestId('messages-container');

      // 确保输入框可见并可交互
      await expect(multilineInput).toBeVisible();
      await expect(multilineInput).toBeEditable();

      // 输入文本
      const testMessage = `多行消息测试-${Date.now()}`;
      await multilineInput.click();
      await multilineInput.fill(testMessage);

      // 验证文本已输入
      await expect(multilineInput).toHaveValue(testMessage);

      // 按 Enter 发送
      await multilineInput.press('Enter');

      // 等待消息被添加，使用更宽松的等待
      await expect(messagesContainer.getByTestId('message-0')).toBeVisible({
        timeout: 5000,
      });

      // 验证消息内容
      await expect(messagesContainer.getByText(testMessage)).toBeVisible();

      // 验证输入框被清空
      await expect(multilineInput).toHaveValue('');
    });

    test('应该支持自动高度调整', async ({ page }) => {
      const multilineInput = page
        .getByTestId('multiline-input')
        .locator('textarea');

      // 获取初始高度
      const initialHeight = await multilineInput.evaluate(
        (el) => (el as HTMLTextAreaElement).offsetHeight
      );

      // 输入多行文本
      const longText = Array(10).fill('这是一行很长的文本').join('\n');
      await multilineInput.fill(longText);

      // 等待高度调整完成，使用自动等待机制
      await expect(multilineInput).toBeVisible();

      // 获取调整后的高度
      const adjustedHeight = await multilineInput.evaluate(
        (el) => (el as HTMLTextAreaElement).offsetHeight
      );

      // 验证高度增加了
      expect(adjustedHeight).toBeGreaterThan(initialHeight);
    });

    test('应该有正确的样式类', async ({ page }) => {
      const multilineInput = page
        .getByTestId('multiline-input')
        .locator('textarea');

      // 验证包含多行模式的样式类
      await expect(multilineInput).toHaveClass(/multi-line/);

      // 验证不包含单行模式的样式类
      const className = await multilineInput.getAttribute('class');
      expect(className).not.toContain('single-line');
    });
  });

  test.describe('单行模式 (multiline: false)', () => {
    test('应该支持 Enter 键发送消息', async ({ page }) => {
      const singlelineInput = page
        .getByTestId('singleline-input')
        .locator('textarea');
      const messagesContainer = page.getByTestId('messages-container');

      // 输入文本
      const testMessage = `单行消息-${Date.now()}`;
      await singlelineInput.click();
      await singlelineInput.fill(testMessage);

      // 验证文本已输入
      await expect(singlelineInput).toHaveValue(testMessage);

      // 按 Enter 键
      await singlelineInput.press('Enter');

      // 等待消息被添加
      await expect(messagesContainer.getByTestId('message-0')).toBeVisible({
        timeout: 5000,
      });

      // 验证消息被发送
      await expect(messagesContainer.getByText(testMessage)).toBeVisible();

      // 验证输入框被清空
      await expect(singlelineInput).toHaveValue('');
    });

    test('不应该支持换行', async ({ page }) => {
      const singlelineInput = page
        .getByTestId('singleline-input')
        .locator('textarea');

      // 尝试输入换行符
      await singlelineInput.fill('第一行');
      await singlelineInput.press('Enter');

      // 由于 Enter 会触发发送，输入框应该被清空
      await expect(singlelineInput).toHaveValue('');
    });

    test('应该有固定高度', async ({ page }) => {
      const singlelineInput = page
        .getByTestId('singleline-input')
        .locator('textarea');

      // 确保元素可见
      await expect(singlelineInput).toBeVisible();

      // 获取初始高度
      const initialHeight = await singlelineInput.evaluate(
        (el) => (el as HTMLTextAreaElement).offsetHeight
      );

      // 尝试输入很长的文本
      const longText = 'A'.repeat(200);
      await singlelineInput.fill(longText);

      // 等待元素稳定
      await expect(singlelineInput).toHaveValue(longText);

      // 获取调整后的高度
      const adjustedHeight = await singlelineInput.evaluate(
        (el) => (el as HTMLTextAreaElement).offsetHeight
      );

      // 验证高度没有变化（或变化很小，允许更大的容差）
      expect(Math.abs(adjustedHeight - initialHeight)).toBeLessThan(10);
    });

    test('应该有正确的样式类', async ({ page }) => {
      const singlelineInput = page
        .getByTestId('singleline-input')
        .locator('textarea');

      // 验证包含单行模式的样式类
      await expect(singlelineInput).toHaveClass(/single-line/);

      // 验证不包含多行模式的样式类
      const className = await singlelineInput.getAttribute('class');
      expect(className).not.toContain('multi-line');
    });
  });

  test.describe('通用功能测试', () => {
    test('应该支持 v-model 双向绑定', async ({ page }) => {
      const multilineInput = page
        .getByTestId('multiline-input')
        .locator('textarea');

      // 输入文本
      const testText = '测试双向绑定';
      await multilineInput.fill(testText);

      // 验证值被正确设置
      await expect(multilineInput).toHaveValue(testText);
    });

    test('应该显示正确的占位符', async ({ page }) => {
      const multilineInput = page
        .getByTestId('multiline-input')
        .locator('textarea');
      const singlelineInput = page
        .getByTestId('singleline-input')
        .locator('textarea');

      // 验证占位符
      await expect(multilineInput).toHaveAttribute(
        'placeholder',
        '支持多行输入，按 Enter 发送'
      );
      await expect(singlelineInput).toHaveAttribute(
        'placeholder',
        '单行输入，按 Enter 发送'
      );
    });

    test('应该正确触发 send 事件', async ({ page }) => {
      const multilineInput = page
        .getByTestId('multiline-input')
        .locator('textarea');
      const singlelineInput = page
        .getByTestId('singleline-input')
        .locator('textarea');
      const messagesContainer = page.getByTestId('messages-container');

      // 测试多行模式发送
      const multilineMessage = `多行消息-${Date.now()}`;
      await multilineInput.click();
      await multilineInput.fill(multilineMessage);
      await multilineInput.press('Enter');

      // 等待第一条消息
      await expect(messagesContainer.getByTestId('message-0')).toBeVisible({
        timeout: 5000,
      });
      await expect(messagesContainer.getByText(multilineMessage)).toBeVisible();

      // 测试单行模式发送
      const singlelineMessage = `单行消息-${Date.now()}`;
      await singlelineInput.click();
      await singlelineInput.fill(singlelineMessage);
      await singlelineInput.press('Enter');

      // 等待第二条消息
      await expect(messagesContainer.getByTestId('message-1')).toBeVisible({
        timeout: 5000,
      });
      await expect(
        messagesContainer.getByText(singlelineMessage)
      ).toBeVisible();
    });

    test('空消息不应该被发送', async ({ page }) => {
      const multilineInput = page
        .getByTestId('multiline-input')
        .locator('textarea');
      const messagesContainer = page.getByTestId('messages-container');

      // 尝试发送空消息
      await multilineInput.press('Enter');

      // 验证没有消息被添加
      await expect(messagesContainer.getByText('暂无消息')).toBeVisible();
    });

    test('只包含空格的消息不应该被发送', async ({ page }) => {
      const multilineInput = page
        .getByTestId('multiline-input')
        .locator('textarea');
      const messagesContainer = page.getByTestId('messages-container');

      // 输入只包含空格的文本
      await multilineInput.fill('   ');
      await multilineInput.press('Enter');

      // 验证没有消息被添加
      await expect(messagesContainer.getByText('暂无消息')).toBeVisible();
    });
  });
});

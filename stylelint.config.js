export default {
  extends: ['stylelint-config-standard'],
  plugins: ['stylelint-order'],
  // 添加自定义规则
  rules: {
    // 允许使用string / url()
    'import-notation': null,
    // 允许空源
    'no-empty-source': null,
    // 允许未知的伪类选择器
    'selector-pseudo-class-no-unknown': true,
    // 允许未知的伪元素选择器
    'selector-pseudo-element-no-unknown': true,
    // 允许使用未知的at规则
    'at-rule-no-unknown': [
      true,
      {
        ignoreAtRules: [
          'tailwind',
          'apply',
          'variants',
          'responsive',
          'screen',
          'function',
          'if',
          'each',
          'include',
          'mixin',
          'reference',
          'theme',
        ],
      },
    ],
    // 允许使用已弃用的at规则
    'at-rule-no-deprecated': [
      true,
      {
        ignoreAtRules: [
          'tailwind',
          'apply',
          'variants',
          'responsive',
          'screen',
          'theme',
        ],
      },
    ],
    // 允许空行
    'no-descending-specificity': null,
    // 允许使用 !important
    'declaration-no-important': null,
    // 不检查属性的前缀
    'property-no-vendor-prefix': null,
    // 不检查值的前缀
    'value-no-vendor-prefix': null,
    // 允许重复选择器（用于媒体查询中）
    'no-duplicate-selectors': null,

    // 强制执行属性排序
    'order/properties-order': [
      {
        // 必须放在最前面的属性
        properties: ['position', 'top', 'right', 'bottom', 'left', 'z-index'],
      },
      {
        // 显示和布局
        properties: [
          'display',
          'visibility',
          'float',
          'clear',
          'overflow',
          'overflow-x',
          'overflow-y',
          'clip',
          'zoom',
        ],
      },
      {
        // FlexBox
        properties: [
          'flex',
          'flex-grow',
          'flex-shrink',
          'flex-basis',
          'flex-flow',
          'flex-direction',
          'flex-wrap',
          'justify-content',
          'align-items',
          'align-content',
          'align-self',
          'order',
        ],
      },
      {
        // Grid
        properties: [
          'grid',
          'grid-template',
          'grid-template-rows',
          'grid-template-columns',
          'grid-template-areas',
          'grid-auto-rows',
          'grid-auto-columns',
          'grid-auto-flow',
          'grid-column-gap',
          'grid-row-gap',
          'grid-gap',
          'grid-column',
          'grid-column-start',
          'grid-column-end',
          'grid-row',
          'grid-row-start',
          'grid-row-end',
          'grid-area',
        ],
      },
      {
        // 盒模型 - 外部
        properties: [
          'box-sizing',
          'width',
          'min-width',
          'max-width',
          'height',
          'min-height',
          'max-height',
          'margin',
          'margin-top',
          'margin-right',
          'margin-bottom',
          'margin-left',
        ],
      },
      {
        // 盒模型 - 内部
        properties: [
          'padding',
          'padding-top',
          'padding-right',
          'padding-bottom',
          'padding-left',
        ],
      },
      {
        // 边框
        properties: [
          'border',
          'border-width',
          'border-style',
          'border-color',
          'border-top',
          'border-top-width',
          'border-top-style',
          'border-top-color',
          'border-right',
          'border-right-width',
          'border-right-style',
          'border-right-color',
          'border-bottom',
          'border-bottom-width',
          'border-bottom-style',
          'border-bottom-color',
          'border-left',
          'border-left-width',
          'border-left-style',
          'border-left-color',
          'border-radius',
          'border-top-left-radius',
          'border-top-right-radius',
          'border-bottom-right-radius',
          'border-bottom-left-radius',
        ],
      },
      {
        // 背景
        properties: [
          'background',
          'background-color',
          'background-image',
          'background-repeat',
          'background-position',
          'background-size',
          'background-attachment',
          'background-clip',
          'background-origin',
        ],
      },
      {
        // 文本
        properties: [
          'color',
          'font',
          'font-family',
          'font-size',
          'font-weight',
          'font-style',
          'font-variant',
          'font-size-adjust',
          'font-stretch',
          'line-height',
          'letter-spacing',
          'text-align',
          'text-decoration',
          'text-indent',
          'text-transform',
          'text-shadow',
          'text-overflow',
          'white-space',
          'word-spacing',
          'word-wrap',
          'word-break',
        ],
      },
      {
        // 其他
        properties: [
          'opacity',
          'cursor',
          'pointer-events',
          'user-select',
          'box-shadow',
          'outline',
          'transition',
          'transform',
          'animation',
        ],
      },
    ],
  },
  // 忽略特定文件
  ignoreFiles: ['**/*.js', '**/*.jsx', '**/*.tsx', '**/*.ts'],
};

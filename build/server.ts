import { mergeConfig, UserConfig } from 'vite';
import { createBaseConfig } from './base';

/**
 * 服务器端生产环境配置
 */
export function createServerConfig(env: Record<string, string>): UserConfig {
  const config: UserConfig = {
    // 优化SSR配置
    ssr: {
      // 外部化依赖，减少服务器包大小
      external: ['react', 'react-dom', 'react-router-dom', 'react-i18next'],
    },
    // 服务器端构建配置
    build: {
      // 指定输出目录
      outDir: 'dist/server',
      // 服务器端不需要代码分割
      rollupOptions: {
        // 指定SSR入口文件
        input: 'src/entry-server.tsx',
        output: {
          format: 'esm',
        },
      },
      // 服务器端不需要压缩
      minify: true,
    },
  };

  return mergeConfig(createBaseConfig(env), config);
}

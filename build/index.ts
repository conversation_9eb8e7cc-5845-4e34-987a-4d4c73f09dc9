import { loadEnv, UserConfig } from 'vite';
import { createDevConfig } from './dev';
import { createClientConfig } from './client';
import { createServerConfig } from './server';

/**
 * 获取配置
 * @param mode 环境模式
 * @param isSsrBuild 是否为SSR构建
 * @returns Vite配置
 */
export function getConfig(mode: string, isSsrBuild: boolean): UserConfig {
  // 加载环境变量
  const env = loadEnv(mode, process.cwd());

  if (mode === 'development') {
    return createDevConfig(env);
  }

  if (isSsrBuild) {
    return createServerConfig(env);
  }

  return createClientConfig(env);
}

import { fileURLToPath, URL } from 'node:url';
import path from 'node:path';
import { UserConfig } from 'vite';
import react from '@vitejs/plugin-react';
import tailwindcss from '@tailwindcss/vite';

function resolve(dir: string) {
  return path.resolve(__dirname, '../', dir);
}

/**
 * 基础配置
 * 包含所有环境共享的配置项
 */
export function createBaseConfig(env: Record<string, string>): UserConfig {
  return {
    base: env.VITE_PUBLIC_PATH || '/',
    plugins: [react(), tailwindcss()],
    resolve: {
      alias: {
        '@': fileURLToPath(new URL('../src', import.meta.url)),
      },
    },
    // 定义全局变量
    define: {
      'process.env.NODE_ENV': JSON.stringify(env.NODE_ENV || 'development'),
    },
    // 优化静态资源目录配置
    publicDir: resolve('public'),
  };
}

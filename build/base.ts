import { fileURLToPath, URL } from 'node:url';
import path from 'node:path';
import { UserConfig } from 'vite';
import vue from '@vitejs/plugin-vue';
import vueJsx from '@vitejs/plugin-vue-jsx';
import tailwindcss from '@tailwindcss/vite';

function resolve(dir: string) {
  return path.resolve(__dirname, '../', dir);
}

/**
 * 基础配置
 * 包含所有环境共享的配置项
 */
export function createBaseConfig(env: Record<string, string>): UserConfig {
  return {
    base: env.VITE_PUBLIC_PATH || '/',
    plugins: [vue(), vueJsx(), tailwindcss()],
    resolve: {
      alias: {
        '@': fileURLToPath(new URL('../src', import.meta.url)),
      },
    },
    // 定义全局变量
    define: {
      __INTLIFY_PROD_DEVTOOLS__: false, // 控制是否在生产环境中启用 Vue I18n Devtools 支持
      __VUE_I18N_FULL_INSTALL__: true, // 控制是否完整安装 Vue I18n
      __VUE_I18N_LEGACY_API__: false, // 控制是否启用 Vue I18n 的旧版 API 支持
      __INTLIFY_JIT_COMPILATION__: false, // 控制是否启用 JIT 编译
    },
    // 优化静态资源目录配置
    publicDir: resolve('public'),
  };
}

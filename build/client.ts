import { mergeConfig, UserConfig } from 'vite';
import { createBaseConfig } from './base';

/**
 * 客户端生产环境配置
 */
export function createClientConfig(env: Record<string, string>): UserConfig {
  const config: UserConfig = {
    // 优化静态资源处理
    build: {
      // 指定输出目录
      outDir: 'dist/client',
      // 小于此大小的资源将被内联为base64，减少HTTP请求
      assetsInlineLimit: 4096,
      // 启用CSS代码分割
      cssCodeSplit: true,
      // 生产环境禁用sourcemap
      sourcemap: false,
      // 自定义构建目标
      target: 'es2020',
      // 优化rollup选项
      rollupOptions: {
        output: {
          // 将大型依赖项拆分为单独的块
          manualChunks: {
            'react-vendor': [
              'react',
              'react-dom',
              'react-router-dom',
              'react-i18next',
            ],
          },
          // 自定义块名称格式
          chunkFileNames: 'static/js/[name]-[hash].js',
          entryFileNames: 'static/js/[name]-[hash].js',
          assetFileNames: 'static/assets/[ext]/[name]-[hash].[ext]',
        },
      },
      // 压缩选项 - 生产环境使用terser进行更彻底的压缩
      minify: 'terser',
      terserOptions: {
        compress: {
          drop_debugger: true, // 移除debugger
          passes: 2, // 多次压缩以获得更好的结果
        },
        format: {
          comments: false, // 移除注释
        },
        mangle: {
          properties: {
            regex: /^_/, // 混淆以_开头的私有属性
          },
        },
      },
      // 启用CSS压缩
      cssMinify: true,
      // 启用代码拆分
      chunkSizeWarningLimit: 500, // 块大小警告限制（KB）
    },
    // 优化预览服务器配置
    preview: {
      // 预览服务器端口
      port: 4173,
    },
  };

  return mergeConfig(createBaseConfig(env), config);
}

import { mergeConfig, UserConfig } from 'vite';
import vueDevTools from 'vite-plugin-vue-devtools';
import { createBaseConfig } from './base';

/**
 * 开发环境配置
 */
export function createDevConfig(env: Record<string, string>): UserConfig {
  const config: UserConfig = {
    plugins: [vueDevTools()],
    // 开发环境特定配置
    server: {
      // 启用HMR
      hmr: true,
    },
    // 优化依赖预构建
    optimizeDeps: {
      // 强制预构建这些依赖
      include: ['vue', 'vue-router', 'pinia', 'vue-i18n'],
      // 排除不需要预构建的依赖
      exclude: [],
    },
    // 开发环境的构建配置
    build: {
      // 开发环境启用源码映射，使用详细模式便于调试
      sourcemap: 'inline',
      // 开发环境不压缩代码，便于调试
      minify: false,
      // 开发环境构建更快
      watch: {},
    },
    // 开发环境的静态资源处理
    assetsInclude: [
      '**/*.svg',
      '**/*.png',
      '**/*.jpg',
      '**/*.jpeg',
      '**/*.gif',
      '**/*.webp',
    ],
  };

  return mergeConfig(createBaseConfig(env), config);
}

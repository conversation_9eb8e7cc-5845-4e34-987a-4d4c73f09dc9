# lefthook.yml
# 全局配置
skip_output:
  - meta
  - success
  - summary

# 预提交钩子
pre-commit:
  parallel: true
  commands:
    lint-oxlint:
      glob: '*.{js,ts,jsx,tsx}'
      run: pnpm lint:oxlint {staged_files}
    lint-eslint:
      glob: '*.{js,ts,jsx,tsx}'
      run: pnpm lint:eslint {staged_files}
    lint-style:
      glob: '*.{css,tsx}'
      run: pnpm lint:style {staged_files}
    format:
      glob: '*.{js,ts,jsx,tsx,css,json,md}'
      run: pnpm format {staged_files}
    unit-tests:
      run: pnpm test:unit

# 提交消息钩子
commit-msg:
  commands:
    validate-commit-msg:
      run: npx --no -- commitlint --edit {1}

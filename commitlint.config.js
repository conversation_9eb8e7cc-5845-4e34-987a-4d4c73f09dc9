// commitlint.config.js
export default {
  extends: ['@commitlint/config-conventional'],
  // 自定义规则
  rules: {
    // 类型枚举
    'type-enum': [
      2,
      'always',
      [
        'feat', // 新功能
        'fix', // 修复
        'docs', // 文档变更
        'style', // 代码格式（不影响代码运行的变动）
        'refactor', // 重构（既不是新增功能，也不是修改bug的代码变动）
        'perf', // 性能优化
        'test', // 增加测试
        'chore', // 构建过程或辅助工具的变动
        'revert', // 回退
        'build', // 打包
        'ci', // CI相关变更
      ],
    ],
    // 类型小写（关闭）
    'type-case': [0],
    // 类型不能为空（关闭，允许为空）
    'type-empty': [0],
    // 范围不能为空（关闭，允许为空）
    'scope-empty': [0],
    // 范围小写（关闭）
    'scope-case': [0],
    // 主题不能为空（开启，必须有提交信息）
    'subject-empty': [2, 'never'],
    // 主题以.为结束标志（关闭）
    'subject-full-stop': [0, 'never'],
    // 主题使用小写（关闭）
    'subject-case': [0, 'never'],
    // body以空行开头（关闭）
    'body-leading-blank': [0],
    // footer以空行开头（关闭）
    'footer-leading-blank': [0],
    // header的最大长度（关闭）
    'header-max-length': [0, 'always', 72],
  },
};

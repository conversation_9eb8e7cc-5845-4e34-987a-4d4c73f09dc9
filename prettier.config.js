/**
 * Prettier 配置文件
 *
 * 这个文件包含了Prettier的推荐配置，并附有详细的注释说明每个选项的作用。
 * 如果您使用的是.prettierrc.json文件，请注意JSON文件不支持注释。
 *
 * 更多信息请参考: https://prettier.io/docs/en/options.html
 */

export default {
  plugins: ['prettier-plugin-tailwindcss'],

  // 每行代码的最大长度
  printWidth: 80,

  // 缩进的空格数
  tabWidth: 2,

  // 是否使用制表符而不是空格进行缩进
  useTabs: false,

  // 是否在语句末尾添加分号
  semi: true,

  // 是否使用单引号而不是双引号
  singleQuote: true,

  // 对象属性是否需要引号
  quoteProps: 'as-needed',

  // 在JSX中是否使用单引号而不是双引号
  jsxSingleQuote: false,

  // 在多行结构中是否添加尾随逗号
  trailingComma: 'es5',

  // 在对象字面量的括号之间是否打印空格
  bracketSpacing: true,

  // 多行JSX元素的>是否放在最后一行的末尾，而不是单独放在下一行
  bracketSameLine: false,

  // 箭头函数参数是否始终加上括号
  arrowParens: 'always',

  // 行尾换行符类型
  endOfLine: 'lf',

  // 是否格式化嵌入在文件中的引用代码
  embeddedLanguageFormatting: 'auto',

  // 是否强制每行只有一个属性
  singleAttributePerLine: false,
};

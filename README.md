# wm-arus-nodejs

## Project Setup

```sh
pnpm install
```

### Compile and Hot-Reload for Development

```sh
pnpm dev
```

### Type-Check, Compile and Minify for Production

```sh
pnpm build
```

### Run Unit Tests with [Vitest](https://vitest.dev/)

```sh
# 运行单元测试（非 watch 模式）
pnpm test:unit

# 运行单元测试（watch 模式，用于开发）
pnpm test:unit:watch
```

### Run End-to-End Tests with [Playwright](https://playwright.dev)

```sh
# Install browsers for the first run
npx playwright install

# When testing on CI, must build the project first
pnpm build

# Runs the end-to-end tests
pnpm test:e2e
# Runs the tests only on Chromium
pnpm test:e2e --project=chromium
# Runs the tests of a specific file
pnpm test:e2e tests/example.spec.ts
# Runs the tests in debug mode
pnpm test:e2e --debug
```

### Lint with [ESLint](https://eslint.org/)

```sh
pnpm lint
```

### Git Hooks with [Lefthook](https://github.com/evilmartians/lefthook)

项目使用 Lefthook 管理 Git 钩子，在安装依赖后会自动设置。

```sh
# 手动安装 Git 钩子
pnpm lefthook:install

# 手动运行所有钩子
pnpm lefthook

# 手动运行特定钩子
pnpm lefthook run pre-commit
```

Git 钩子配置：

- `pre-commit`: 在提交前自动运行代码检查、格式化和单元测试（如果单元测试失败，提交将被阻止）
- `commit-msg`: 验证提交消息是否符合 Conventional Commits 规范

### 规范化提交信息

项目使用 [Commitizen](http://commitizen.github.io/cz-cli/) 和 [Commitlint](https://commitlint.js.org/) 来规范化 Git 提交信息。

```sh
# 使用交互式提交工具（推荐）
pnpm commit

# 或者使用原生 git 命令（需要遵循 Conventional Commits 规范）
git commit -m "feat: 添加新功能"
```

提交信息格式规范：

```
[type][(scope)]: <subject>

[body]

[footer]
```

**注意**：

- 只有 `<subject>` (提交描述) 是必填的
- `[type]`、`[(scope)]`、`[body]` 和 `[footer]` 都是可选的

常用的 type 类型:

- `feat`: 新功能
- `fix`: 修复 Bug
- `docs`: 文档变更
- `style`: 代码格式（不影响代码运行的变动）
- `refactor`: 重构（既不是新增功能，也不是修改 bug 的代码变动）
- `perf`: 性能优化
- `test`: 增加测试
- `chore`: 构建过程或辅助工具的变动
- `revert`: 回退
- `build`: 打包
- `ci`: CI相关变更

import { fileURLToPath } from 'node:url';
import { loadEnv } from 'vite';
import { mergeConfig, defineConfig, configDefaults } from 'vitest/config';
import { createDevConfig } from './build/dev';
// 创建测试配置
export default mergeConfig(
  // 使用基础配置
  createDevConfig(loadEnv('test', process.cwd())),
  defineConfig({
    // 测试特定配置
    test: {
      environment: 'jsdom',
      exclude: [...configDefaults.exclude, 'e2e/**'],
      root: fileURLToPath(new URL('./', import.meta.url)),
      // 启用覆盖率报告
      coverage: {
        provider: 'v8',
        reporter: ['text', 'json', 'html'],
      },
    },
  })
);

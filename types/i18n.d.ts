import type zhMO from '@/i18n/zh-MO';

/**
 * 应用的 i18n 消息类型定义
 * 基于 zh-MO 的消息结构自动推断类型
 */
export type MessageSchema = typeof zhMO;

/**
 * 从嵌套对象类型中提取所有可能的键路径
 * 例如: { a: { b: { c: string } } } => 'a' | 'a.b' | 'a.b.c'
 */
export type NestedKeyOf<ObjectType extends object> = {
  [Key in keyof ObjectType & (string | number)]: ObjectType[Key] extends object
    ? `${Key}` | `${Key}.${NestedKeyOf<ObjectType[Key]>}`
    : `${Key}`;
}[keyof ObjectType & (string | number)];

/**
 * 从嵌套对象类型中提取指定前缀下的键路径
 * 例如: GetNestedKeys<MessageSchema, 'home'> => 'text.welcome' | 'placeholder.input'
 */
export type GetNestedKeys<
  T extends object,
  Prefix extends string,
> = Prefix extends keyof T
  ? T[Prefix] extends object
    ? NestedKeyOf<T[Prefix]>
    : never
  : never;

/**
 * 所有可用的翻译键路径
 */
export type TranslationKey = NestedKeyOf<MessageSchema>;

/**
 * 命名空间翻译键类型
 */
export type NamespaceTranslationKey<T extends string> =
  T extends keyof MessageSchema
    ? GetNestedKeys<MessageSchema, T>
    : TranslationKey;

// 注释掉全局类型扩展，避免与局部 i18n 实例冲突
// declare module 'vue-i18n' {
//   // 扩展 vue-i18n 的类型定义
//   export interface DefineLocaleMessage extends MessageSchema {}
// }

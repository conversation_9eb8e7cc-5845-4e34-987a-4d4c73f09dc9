/**
 * 事件总线支持的所有事件类型定义
 *
 * 对于不需要传递数据的事件，使用 undefined 类型而非 void
 * 这样可以避免类型检查问题，并且更符合实际使用场景
 */
declare type MittEvents = {
  // 认证相关事件
  userUnauthorizedError: undefined;
  invalidAccessTokenError: undefined;
  invalidRefreshTokenError: undefined;

  // 业务相关事件
  catchAccountError: string;
};

/**
 * 事件处理器类型
 */
declare type EventHandler<T> = (event: T) => void;

/**
 * 事件总线接口定义
 */
declare interface EventBus {
  /**
   * 监听指定类型的事件
   * @param event 事件类型
   * @param handler 事件处理函数
   * @returns 取消订阅的函数
   */
  on<K extends keyof MittEvents>(
    event: K,
    handler: EventHandler<
      MittEvents[K] extends undefined ? void : MittEvents[K]
    >
  ): () => void;

  /**
   * 发送指定类型的事件
   * @param event 事件类型
   * @param data 事件数据（对于undefined类型的事件可以省略）
   */
  emit<K extends keyof MittEvents>(
    event: K,
    data?: MittEvents[K] extends undefined ? never : MittEvents[K]
  ): void;

  /**
   * 清除所有事件监听
   */
  clearAll(): void;
}

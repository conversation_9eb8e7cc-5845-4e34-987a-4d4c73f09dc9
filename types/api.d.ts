import type { AxiosError, ResponseType } from 'axios';

declare global {
  /**
   * API错误响应接口
   */
  interface ApiErrorResponse {
    /**
     * 错误列表，包含错误代码和错误信息
     */
    errorList?: {
      [key: string]: string;
    };
    [key: string]: unknown;
  }

  /**
   * API错误接口，扩展自Axios错误
   */
  interface ApiError extends AxiosError<ApiErrorResponse> {
    /**
     * 错误代码
     */
    code: string;
  }

  interface CreateRequestApiOptions {
    /**
     * API的基础URL
     */
    baseURL?: string;

    /**
     * 请求超时时间（毫秒）
     */
    timeout?: number;

    /**
     * 响应类型
     */
    responseType?: ResponseType;

    /**
     * 请求头
     */
    headers?: Record<string, string>;

    /**
     * 用于签名的盐值
     */
    salt?: string;
  }
}

export {};

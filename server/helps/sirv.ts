import type { ServerResponse } from 'node:http';
import type { Stats } from 'node:fs';

interface GetSirvOptionsOptions {
  isProd: boolean;
  maxAge?: number;
}

export function getSirvOptions({ isProd, maxAge }: GetSirvOptionsOptions) {
  // 创建一个自定义的setHeaders函数，用于设置XSS保护头
  function setXssProtectionHeader(
    res: ServerResponse,
    _pathname: string,
    _stats: Stats
  ) {
    res.setHeader('X-XSS-Protection', '1');
  }

  // sirv配置 - 基础选项
  const sirvBaseOptions = {
    setHeaders: setXssProtectionHeader,
    dev: !isProd, // 开发模式下禁用缓存
    etag: true, // 启用ETag
    gzip: true, // 启用gzip压缩
    brotli: true, // 启用brotli压缩
    immutable: isProd, // 生产环境下设置immutable缓存控制
    maxAge: isProd ? maxAge : 0, // 生产环境下缓存1年，开发环境不缓存
  };

  // 通用静态资源配置
  const sirvOptions = {
    ...sirvBaseOptions,
  };

  const faviconOptions = {
    ...sirvOptions,
    maxAge,
    single: true, // 只提供单个文件
  };

  const staticOptions = {
    ...sirvOptions,
    // 根据文件类型设置不同的缓存策略
    setHeaders: (res: ServerResponse, pathname: string, _stats: Stats) => {
      // 基础安全头
      setXssProtectionHeader(res, pathname, _stats);
      res.setHeader('Cache-Control', `public, max-age=${maxAge}`);
    },
  };

  const clientOptions = {
    ...sirvOptions,
    extensions: [], // 不自动添加扩展名
  };

  return {
    sirvOptions,
    faviconOptions,
    staticOptions,
    clientOptions,
  };
}

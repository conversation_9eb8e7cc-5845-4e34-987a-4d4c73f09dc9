export function createPass(options: { publicPath: string }) {
  const { publicPath } = options;

  const whitelist = [
    '^/__mock__/.+$',
    `^${publicPath}favicon.ico$`, // favicon图标，浏览器会自动访问该地址
    `^${publicPath}static/\\S+$`, // 静态文件目录
  ];

  return (req: Request) => {
    for (let i = 0; i < whitelist.length; i += 1) {
      if (new RegExp(whitelist[i], 'g').test(req.url)) {
        return true;
      }
    }

    return false;
  };
}

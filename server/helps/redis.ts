import Redis from 'ioredis';
import type { NatMap } from 'ioredis';

/**
 * 解析Redis NAT映射字符串
 *
 * @param natMapStr NAT映射字符串，格式: "********:30001=>external-host-1.io:6379, ********:30002=>external-host-2.io:6379"
 * @returns NAT映射对象
 */
function parseNatMap(natMapStr: string): NatMap {
  if (!natMapStr) return {};

  const natMap: NatMap = {};
  const mappings = natMapStr.split(',').map((item: string) => item.trim());

  for (const mapping of mappings) {
    const [internal, external] = mapping
      .split('=>')
      .map((item: string) => item.trim());
    if (internal && external) {
      const [externalHost, externalPortStr] = external.split(':');
      const externalPort = parseInt(externalPortStr, 10);
      if (externalHost && !isNaN(externalPort)) {
        natMap[internal] = { host: externalHost, port: externalPort };
      }
    }
  }

  return natMap;
}

export interface CreateRedisClientOptions {
  /**
   * Redis URL
   */
  redisUrl: string;

  /**
   * 是否启用集群模式
   */
  isCluster: boolean;

  /**
   * NAT映射字符串
   */
  natMapStr: string;

  /**
   * 键前缀
   */
  keyPrefix: string;
}

/**
 * 创建Redis客户端实例
 *
 * @param options Redis客户端配置选项
 * @returns Redis客户端实例
 */
export function createRedisClient(options: CreateRedisClientOptions): Redis {
  // 从环境变量中获取Redis配置
  const { redisUrl, isCluster, natMapStr, keyPrefix } = options;

  // 重试策略
  const retryStrategy = (times: number): number | null => {
    // 前 10 次快速重试（网络抖动）
    if (times <= 10) {
      return times * 100; // 100ms, 200ms, ..., 1000ms
    }

    // 11-30 次中等延迟重试（短期故障）
    if (times <= 30) {
      return 2000; // 固定 2 秒
    }

    // 31-60 次长延迟重试（长期故障）
    if (times <= 60) {
      return 10000; // 固定 10 秒
    }

    // 超过 60 次停止重试
    return null;
  };

  // 基础配置
  const baseOptions = {
    connectTimeout: 10000,
    retryStrategy,
    enableOfflineQueue: true,
    keyPrefix,
  };

  let client: Redis;

  // 根据是否启用集群模式创建不同的Redis客户端
  if (isCluster) {
    // 集群模式
    const nodes = redisUrl.split(',').map((node: string) => node.trim());
    const natMap = parseNatMap(natMapStr);

    client = new Redis.Cluster(nodes, {
      redisOptions: baseOptions,
      natMap: Object.keys(natMap).length > 0 ? natMap : undefined,
    }) as unknown as Redis;

    console.log('[Redis] Redis Cluster mode enabled with nodes:', nodes);
    if (Object.keys(natMap).length > 0) {
      console.log('[Redis] Redis NAT mapping enabled:', natMap);
    }
  } else {
    // 单节点模式
    client = new Redis(redisUrl, baseOptions);
    console.log('[Redis] Redis Standalone mode enabled with URL:', redisUrl);
  }

  // 监听连接事件
  client.on('connect', () => {
    console.log('[Redis] Redis client connected');
  });

  // 监听错误事件
  client.on('error', (err) => {
    console.error('[Redis] Redis client error:', err);
  });

  return client;
}

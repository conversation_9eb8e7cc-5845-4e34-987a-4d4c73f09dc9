declare module 'cas-authentication' {
  import { Request, Response, NextFunction } from 'express';

  /**
   * CAS 认证选项
   */
  interface CASOptions {
    /**
     * CAS 服务器 URL
     */
    cas_url: string;

    /**
     * 服务 URL
     */
    service_url: string;

    /**
     * CAS 版本
     */
    cas_version?: string;

    /**
     * 是否每次都重新认证
     */
    renew?: boolean;

    /**
     * 是否开发模式
     */
    is_dev_mode?: boolean;

    /**
     * 开发模式用户
     */
    dev_mode_user?: string;

    /**
     * 开发模式用户信息
     */
    dev_mode_info?: { attributes: Record<string, unknown> };

    /**
     * 会话名称
     */
    session_name?: string;

    /**
     * 会话信息名称
     */
    session_info?: string;

    /**
     * 是否销毁会话
     */
    destroy_session?: boolean;

    /**
     * CAS 服务器端口
     */
    cas_port?: number;
  }

  /**
   * CAS 认证类型
   */
  enum AUTH_TYPE {
    BOUNCE = 0,
    BOUNCE_REDIRECT = 1,
    BLOCK = 2,
  }

  /**
   * CAS 认证类
   */
  class CASAuthentication {
    /**
     * CAS 服务器 URL
     */
    cas_url: string;

    /**
     * 服务 URL
     */
    service_url: string;

    /**
     * CAS 版本
     */
    cas_version: string;

    /**
     * 是否每次都重新认证
     */
    renew: boolean;

    /**
     * 是否开发模式
     */
    is_dev_mode: boolean;

    /**
     * 开发模式用户
     */
    dev_mode_user: string;

    /**
     * 开发模式用户信息
     */
    dev_mode_info: { attributes: Record<string, unknown> };

    /**
     * 会话名称
     */
    session_name: string;

    /**
     * 会话信息名称
     */
    session_info: string;

    /**
     * 是否销毁会话
     */
    destroy_session: boolean;

    /**
     * CAS 服务器端口
     */
    cas_port: number;

    /**
     * 构造函数
     * @param options CAS 选项
     */
    constructor(options: CASOptions);

    /**
     * 跳转到 CAS 登录页面
     */
    bounce(req: Request, res: Response, next: NextFunction): void;

    /**
     * 跳转到 CAS 登录页面并重定向
     */
    bounceRedirect(req: Request, res: Response, next: NextFunction): void;

    /**
     * 阻止未认证的请求
     */
    block(req: Request, res: Response, next: NextFunction): void;

    /**
     * 登出
     */
    logout(req: Request, res: Response, next: NextFunction): void;

    /**
     * 处理票据
     */
    _handleTicket(req: Request, res: Response, next: NextFunction): void;

    /**
     * 登录
     */
    _login(req: Request, res: Response): void;

    /**
     * 处理请求
     */
    _handle(
      req: Request,
      res: Response,
      next: NextFunction,
      authType: AUTH_TYPE
    ): void;
  }

  export = CASAuthentication;
}

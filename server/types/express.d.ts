// 导入Express类型，确保类型扩展正确应用
import 'express';
import 'express-session';

// 扩展Express的类型定义
declare global {
  namespace Express {
    // 扩展Response.locals接口，添加locale属性
    interface Locals {
      locale: string;
      appLoggedIn: boolean;
      userCategory: 'TEACHER' | 'STUDENT';
    }
  }
}

// 扩展Session接口，添加自定义属性
declare module 'express-session' {
  interface SessionData {
    cas_return_to?: string;
    cas_user?: string;
    cas_userinfo?: {
      attributes: Record<string, unknown>;
    };
    appToken?: {
      accessToken: string;
      refreshToken: string;
      expiresIn: string;
      userCategory?: string;
    };
    // 允许动态添加属性，用于 CAS 中间件
    [key: string]: unknown;
  }
}

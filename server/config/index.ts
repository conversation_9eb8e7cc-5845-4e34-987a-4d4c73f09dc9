import { config } from 'dotenv';
import url from 'node:url';

import { getPublicPath } from '../helps/path';

config();

// 服务端配置
export const PORT = process.env.VITE_EXPRESS_PORT
  ? parseInt(process.env.VITE_EXPRESS_PORT)
  : 8080;
export const IS_PROD = process.env.NODE_ENV === 'production';
export const PUBLIC_PATH = getPublicPath();
const localhostUrl =
  PORT === 80 ? 'http://localhost' : `http://localhost:${PORT}`;
const serviceUrlString = process.env.SERVICE_URL || localhostUrl;
const serviceUrl = new url.URL(serviceUrlString);
export const SERVICE_URL_STRING = serviceUrlString;

// 静态资源配置
export const STATIC_MAX_AGE = 365 * 24 * 60 * 60 * 1000; // 单位为ms，共1年;

// Cookie配置
// 是否使用https
export const COOKIE_SECURE = serviceUrl.protocol === 'https:';
// 是否只在当前站点发送
export const COOKIE_SAMESITE = COOKIE_SECURE ? 'strict' : 'lax';

// Redis配置
export const REDIS_URL = process.env.VITE_REDIS_URL || 'redis://localhost:6379';
export const REDIS_CLUSTER = process.env.VITE_REDIS_CLUSTER === 'on';
export const REDIS_NAT_MAP = process.env.VITE_REDIS_NAT_MAP || '';
export const REDIS_PREFIX = process.env.VITE_REDIS_PREFIX || 'wm:arus:';

// Session配置
// Session密钥，每个应用应该不同
export const SESSION_SECRET = 'wm-arus-session-secret';
// Session ID的cookie名
export const SESSION_ID_COOKIE_KEY =
  process.env.VITE_SESSION_ID_COOKIE_KEY || 'wm.arus.sid';

// 语言配置
// 语言cookie名
export const LOCALE_COOKIE_KEY =
  process.env.VITE_LOCALE_COOKIE_KEY || 'wm.locale';
// 语言参数名
export const LOCALE_PARAM_KEY = process.env.VITE_LOCALE_PARAM_KEY || 'locale';
// 默认语言
export const FALLBACK_LOCALE = process.env.VITE_FALLBACK_LOCALE || 'zh_MO';
// 语言cookie过期时间
export const LOCALE_COOKIE_MAX_AGE =
  process.env.VITE_LOCALE_COOKIE_MAX_AGE || 60 * 24 * 30 * 1000; // 默认30天;

// CAS 配置
// CAS 基础 URL
export const CAS_BASE_URL = process.env.VITE_CAS_BASE_URL || '';
// CAS 版本
export const CAS_VERSION = process.env.VITE_CAS_VERSION || '3.0';
// CAS 会话名称
export const CAS_SESSION_NAME = process.env.VITE_CAS_SESSION_NAME || 'cas_user';
// CAS 会话信息名称
export const CAS_SESSION_INFO =
  process.env.VITE_CAS_SESSION_INFO || 'cas_userinfo';
// 令牌会话键名
export const TOKEN_SESSION_KEY =
  process.env.VITE_TOKEN_SESSION_KEY || 'appToken';
// 客户端 ID
export const CLIENT_ID = process.env.VITE_CLIENT_ID || '';
// 客户端密钥
export const CLIENT_SECRET = process.env.VITE_CLIENT_SECRET || '';

// 接口配置
// 數據中心API
export const DC_API_BASE_URL = process.env.VITE_DC_API_BASE_URL;

// 令牌接口地址
export const TOKEN_API_BASE_URL = `${DC_API_BASE_URL}/api/v1`;

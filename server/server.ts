import express from 'express';
import path from 'node:path';
import { fileURLToPath } from 'node:url';
import cookieParser from 'cookie-parser';
import helmet from 'helmet';
import sirv from 'sirv';
import Redlock from 'redlock';
import type { CompatibleRedisClient } from 'redlock';

import * as config from './config';
import { getSirvOptions } from './helps/sirv';
import { createRedisClient } from './helps/redis';
import { createServerRenderMiddleware } from './middlewares/server-render';
import { createLocaleMiddleware } from './middlewares/locale';
import { createErrorHandlerMiddleware } from './middlewares/error';
import { createSessionMiddleware } from './middlewares/session';
import { createCASMiddlewares } from './middlewares/cas';
import { createUserCategoryMiddleware } from './middlewares/user-category';

function resolve(...args: string[]) {
  return path.resolve(path.dirname(fileURLToPath(import.meta.url)), ...args);
}

// 当收到未捕获的异常时，退出进程
process.on('uncaughtException', (error) => {
  // 记录未捕获的异常错误日志
  console.error('[Express] UncaughtException', error);
  // 手动退出进程，pm2会自动重启
  process.exit(1);
});

const app = express();

// 如果使用HTTPS，需要设置X-Forwarded-Proto头，防止express-session中的Cookie解密失败
if (config.COOKIE_SECURE) {
  app.set('trust proxy', 1); // trust first proxy
  app.use((req, _res, next) => {
    req.headers['x-forwarded-proto'] = 'https';
    next();
  });
}

// 使用helmet中间件添加安全头
app.use(
  helmet({
    contentSecurityPolicy: false,
    crossOriginEmbedderPolicy: false,
    crossOriginOpenerPolicy: false,
    crossOriginResourcePolicy: false,
  })
);

const { faviconOptions, staticOptions, clientOptions, sirvOptions } =
  getSirvOptions({
    isProd: config.IS_PROD,
    maxAge: config.STATIC_MAX_AGE,
  });

if (config.IS_PROD) {
  // 提供favicon
  app.use(
    `${config.PUBLIC_PATH}favicon.ico`,
    sirv(resolve('../public'), faviconOptions)
  );

  // 提供student-favicon
  app.use(
    `${config.PUBLIC_PATH}student-favicon.ico`,
    sirv(resolve('../public'), faviconOptions)
  );

  // 提供静态资源目录
  app.use(
    `${config.PUBLIC_PATH}static/`,
    sirv(resolve('../dist/client/static/'), staticOptions)
  );
} else {
  // 开发环境下的静态文件服务
  app.use(
    sirv(resolve('../src/assets'), {
      ...sirvOptions,
      dotfiles: false, // 不提供以点开头的文件
    })
  );
}

// 使用cookie-parser中间件解析cookie
app.use(cookieParser());

// 创建Redis客户端
const redisClient = createRedisClient({
  redisUrl: config.REDIS_URL,
  isCluster: config.REDIS_CLUSTER,
  natMapStr: config.REDIS_NAT_MAP,
  keyPrefix: config.REDIS_PREFIX,
});

process.on('exit', () => {
  if (redisClient) {
    console.log('[Redis] Closing Redis connection');
    redisClient.quit();
  }
});

// 使用session中间件，连接Redis存储会话数据
app.use(
  createSessionMiddleware({
    redisClient,
    secret: config.SESSION_SECRET,
    name: config.SESSION_ID_COOKIE_KEY,
    secure: config.COOKIE_SECURE,
    sameSite: config.COOKIE_SAMESITE,
  })
);

// 配置body解析的基本选项
const bodyParserOptions = {
  limit: '2gb', // 限制请求体大小为2GB
};

// 解析JSON请求体 - 用于API请求
app.use(
  express.json({
    ...bodyParserOptions,
    strict: true, // 只接受数组和对象
  })
);

// 解析URL编码的请求体 - 用于表单提交
app.use(
  express.urlencoded({
    ...bodyParserOptions,
    extended: true, // 使用qs库解析复杂对象
  })
);

// 使用locale中间件处理语言设置
app.use(
  createLocaleMiddleware({
    cookieKey: config.LOCALE_COOKIE_KEY,
    paramKey: config.LOCALE_PARAM_KEY,
    fallbackLocale: config.FALLBACK_LOCALE,
    cookieMaxAge: config.LOCALE_COOKIE_MAX_AGE,
    secure: config.COOKIE_SECURE,
    sameSite: config.COOKIE_SAMESITE,
  })
);

// 创建 Redlock 实例，用于分布式锁
// 使用 any 类型来避免类型检查问题
const redlock = new Redlock([redisClient as unknown as CompatibleRedisClient], {
  // 漂移时间
  driftFactor: 0.01,
  // 重试次数
  retryCount: 10,
  // 重试延迟（毫秒）
  retryDelay: 200,
  // 重试抖动（毫秒）
  retryJitter: 200,
});

redlock.on('clientError', (err) => {
  console.error(
    '[Redlock] A redis error has occurred:',
    (err as Error).stack || ''
  );
});

// 创建 CAS 中间件
const {
  createCasAuthentication,
  createCasTicketMiddlewares,
  createTokenMiddlewares,
  createLoginStatusMiddleware,
} = createCASMiddlewares();

// 创建 CAS 认证实例
const CASAuthentication = createCasAuthentication();
const cas = new CASAuthentication({
  cas_url: config.CAS_BASE_URL,
  service_url: config.SERVICE_URL_STRING,
  cas_version: config.CAS_VERSION,
  session_info: config.CAS_SESSION_INFO,
  // 开发模式设置 - 当没有配置CAS_BASE_URL时启用开发模式
  is_dev_mode: !config.CAS_BASE_URL || config.CAS_BASE_URL === '',
  dev_mode_user: 'dev_user',
  dev_mode_info: { attributes: { userCategory: 'TEACHER' } },
});

// 创建 CAS 票据中间件
const { casTicketSessionMapperMiddleware, casSloMiddleware } =
  createCasTicketMiddlewares({
    redisClient,
    casUserSessionKey: config.CAS_SESSION_NAME,
    tokenSessionKey: config.TOKEN_SESSION_KEY,
  });

// 使用 CAS 中间件
app.use(casTicketSessionMapperMiddleware);
// 暂时注释掉有问题的路由
// app.post('/*', casSloMiddleware);

// 创建Token中间件
const {
  blockTokenMiddleware,
  renewTokenMiddleware,
  replaceRequestTokenMiddleware,
  removeTokenMiddleware,
} = createTokenMiddlewares({
  redlock,
  clientId: config.CLIENT_ID,
  clientSecret: config.CLIENT_SECRET,
  baseUrl: config.TOKEN_API_BASE_URL,
  casUserSessionKey: config.CAS_SESSION_NAME,
  tokenSessionKey: config.TOKEN_SESSION_KEY,
  redisPrefix: config.REDIS_PREFIX,
});

// Token中间件
app.get(`${config.PUBLIC_PATH}logout`, removeTokenMiddleware, cas.logout);
app.use(blockTokenMiddleware); // 当用户CAS身份失效时执行，返回401状态码
app.use(cas.bounce); // CAS登入
app.use(renewTokenMiddleware); // 刷新Token
app.use(replaceRequestTokenMiddleware); // 替换express的locals变量

// 创建用户类型中间件
const userCategoryMiddleware = createUserCategoryMiddleware({
  tokenSessionKey: config.TOKEN_SESSION_KEY,
});
// 使用用户类型中间件
app.use(userCategoryMiddleware);

// 创建登录状态中间件
const loginStatusMiddleware = createLoginStatusMiddleware({
  casUserSessionKey: config.CAS_SESSION_NAME,
});
// 使用登录状态中间件
app.use(loginStatusMiddleware);

// 开发模式下使用vite的中间件, 生产模式下使用compression
let vite;
if (!config.IS_PROD) {
  const { createServer } = await import('vite');
  vite = await createServer({
    server: { middlewareMode: true },
    appType: 'custom',
    base: config.PUBLIC_PATH,
    configFile: resolve('../vite.config.ts'),
    mode: 'development',
  });
  app.use(vite.middlewares);
} else {
  const compression = (await import('compression')).default;
  app.use(compression());
  app.use(config.PUBLIC_PATH, sirv('./dist/client', clientOptions));
}

// 服务端渲染
const serverRenderMiddleware = createServerRenderMiddleware({
  vite,
  isProd: config.IS_PROD,
  publicPath: config.PUBLIC_PATH,
  entryServerPath: config.IS_PROD
    ? resolve('../dist/server/entry-server.js')
    : resolve('../src/entry-server.tsx'),
  templatePath: config.IS_PROD
    ? resolve('../dist/client/index.html')
    : resolve('../index.html'),
});
// 处理所有GET请求的服务端渲染 - 使用Express 4兼容的通配符语法
app.get('*', serverRenderMiddleware);

// 错误处理
const errorHandlerMiddleware = createErrorHandlerMiddleware({ cas });
app.use(errorHandlerMiddleware);

// 启动服务器
app.listen(config.PORT, () => {
  console.log(`[Express] Express server listen in ${config.PORT}`);
});

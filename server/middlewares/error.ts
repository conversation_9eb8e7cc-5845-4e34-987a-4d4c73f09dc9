import type {
  Request,
  Response,
  NextFunction,
  ErrorRequestHandler,
} from 'express';
import { CasAuthenticationInstance } from './cas/types';

export interface CreateErrorHandlerMiddlewareOptions {
  cas: CasAuthenticationInstance;
}

export function createErrorHandlerMiddleware(
  options: CreateErrorHandlerMiddlewareOptions
): ErrorRequestHandler {
  return (
    error: BaseError,
    req: Request,
    res: Response,
    next: NextFunction
  ): void => {
    // 以下三种情况不需要处理
    // 1、没有出错
    // 2、AJAX请求
    // 3、不是GET请求
    if (!error || req.xhr || req.method.toLowerCase() !== 'get') {
      next();
      return;
    }

    const { cas } = options;
    switch (error.code) {
      case 'ROUTE_NOT_FOUND':
        console.error(
          '[ErrorHandler]',
          new Date().toISOString(),
          'errorHandler',
          error
        );
        res.status(404).send('Page not found');
        break;

      case 'LOGIN_REQUIRED':
      case 'F_REFRESH_TOKEN_FAILED': // 无法刷新Token，可能是CAS认证失败，由Token中间件输出
        // CAS认证由这里发起
        cas._login(req, res);
        break;

      case 'F_GET_TOKEN_FAILED': // 无法获取Token，可能是CAS认证失败，由Token中间件输出
        res.status(500).send(`Login Failed (code: ${error.code})`).end();
        break;

      default:
        console.error(new Date().toISOString(), 'errorHandler', error);
        res.status(500).send('Internal server error').end();
        break;
    }
  };
}

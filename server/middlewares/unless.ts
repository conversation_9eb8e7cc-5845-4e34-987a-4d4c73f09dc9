import type { Request, Response, NextFunction } from 'express';

export function createUnlessMiddleware(
  pred: (req: Request) => boolean,
  middleware: Middleware
) {
  return function UnlessMiddleware(
    req: Request,
    res: Response,
    next: NextFunction
  ) {
    if (pred(req)) {
      next(); // Skip this middleware.
    } else {
      middleware(req, res, next); // Allow this middleware.
    }
  };
}

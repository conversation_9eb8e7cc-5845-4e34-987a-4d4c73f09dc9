import type { Request, Response, NextFunction } from 'express';

export function createXXssProtectionMiddleware() {
  function setXssProtectionHeader(res: Response) {
    res.setHeader('X-XSS-Protection', '1');
  }

  function xXssProtectionMiddleware(
    req: Request,
    res: Response,
    next: NextFunction
  ) {
    setXssProtectionHeader(res);
    next();
  }

  return {
    setXssProtectionHeader,
    xXssProtectionMiddleware,
  };
}

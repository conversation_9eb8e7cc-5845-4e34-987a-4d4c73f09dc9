import fs from 'node:fs/promises';
import type { Request, Response, NextFunction } from 'express';
import type { ViteDevServer } from 'vite';

interface ServerRenderMiddlewareOptions {
  publicPath: string;
  isProd: boolean;
  templatePath: string;
  entryServerPath: string;
  vite?: ViteDevServer;
}

interface RenderOptions {
  url: string;
  locale: string;
  appLoggedIn: boolean;
  userCategory: 'TEACHER' | 'STUDENT';
}

export function createServerRenderMiddleware(
  options: ServerRenderMiddlewareOptions
) {
  return async function ServerRenderMiddleware(
    req: Request,
    res: Response,
    next: NextFunction
  ) {
    try {
      const url = req.originalUrl;

      let template: string;
      let render: (options: RenderOptions) => Promise<{
        html: string;
        head?: string;
        initialState?: Record<string, unknown>;
      }>;
      if (!options.isProd && options.vite) {
        // Always read fresh template in development
        template = await fs.readFile(options.templatePath, 'utf-8');
        template = await options.vite.transformIndexHtml(url, template);
        render = (await options.vite.ssrLoadModule(options.entryServerPath))
          .render;
      } else {
        template = await fs.readFile(options.templatePath, 'utf-8');
        render = (await import(options.entryServerPath)).render;
      }

      // 将locale传递给render函数
      const rendered = await render({
        url,
        ...res.locals,
      });

      // 注入初始状态到HTML中
      const initialStateScript = rendered.initialState
        ? `<script>window.__INITIAL_STATE__ = ${JSON.stringify(rendered.initialState)}</script>`
        : '';

      const html = template
        .replace(`<!--app-head-->`, rendered.head ?? '')
        .replace(`<!--app-html-->`, rendered.html ?? '')
        .replace(`<!--app-initial-state-->`, initialStateScript);

      res.status(200).set({ 'Content-Type': 'text/html' }).send(html);
    } catch (error) {
      const e = error as Error;
      options.vite?.ssrFixStacktrace(e);
      next(e);
    }
  };
}

import type { Request, Response, NextFunction } from 'express';

interface LocaleMiddlewareOptions {
  /**
   * Cookie名称，用于存储locale信息
   */
  cookieKey: string;

  /**
   * URL参数名称，用于从URL中获取locale
   */
  paramKey: string;

  /**
   * 默认语言，当无法从URL或Cookie中获取locale时使用
   */
  fallbackLocale: string;

  /**
   * Cookie过期时间（天）
   */
  cookieMaxAge: number | string;

  /**
   * Cookie是否只在HTTPS下发送
   */
  secure: boolean;

  /**
   * Cookie是否只在当前站点发送
   */
  sameSite: boolean | 'lax' | 'strict' | 'none';
}

/**
 * 创建处理locale的中间件
 *
 * 中间件会按照以下顺序获取locale:
 * 1. 从URL参数中获取
 * 2. 从Cookie中获取
 * 3. 使用默认值
 *
 * 如果Cookie中没有locale或与获取到的locale不一致，则会设置新的locale到Cookie中
 */
export function createLocaleMiddleware(options: LocaleMiddlewareOptions) {
  const {
    cookieKey,
    paramKey,
    fallbackLocale,
    cookieMaxAge,
    secure,
    sameSite,
  } = options;

  return function localeMiddleware(
    req: Request,
    res: Response,
    next: NextFunction
  ) {
    // 1. 从URL参数中获取locale
    const urlLocale = req.query[paramKey] as string | undefined;

    // 2. 从Cookie中获取locale
    const cookieLocale = req.cookies[cookieKey];

    // 3. 确定使用的locale
    let locale: string;

    if (urlLocale) {
      // 使用URL中的locale
      locale = urlLocale;
    } else if (cookieLocale) {
      // 使用Cookie中的locale
      locale = cookieLocale;
    } else {
      // 使用默认locale
      locale = fallbackLocale;
    }

    // 4. 如果Cookie中没有locale或与获取到的locale不一致，则设置新的locale到Cookie中
    if (!cookieLocale || cookieLocale !== locale) {
      const maxAge =
        typeof cookieMaxAge === 'string'
          ? parseInt(cookieMaxAge)
          : cookieMaxAge;
      res.cookie(cookieKey, locale, {
        maxAge, // 转换为毫秒
        httpOnly: true,
        secure,
        sameSite,
      });
    }

    // 5. 将locale添加到请求对象中，以便后续中间件和路由处理程序使用
    res.locals.locale = locale;

    next();
  };
}

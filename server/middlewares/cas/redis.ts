/**
 * CAS 中间件 Redis 工具函数
 */
import type { Redis } from 'ioredis';

/**
 * 创建 Redis 工具函数
 * @returns Redis 工具函数集合
 */
export function createRedisUtils() {
  /**
   * 从 Redis 获取值
   * @param redisClient Redis 客户端
   * @param key 键名
   * @returns 键值
   */
  function getRedisValue(
    redisClient: Redis,
    key: string
  ): Promise<string | null> {
    return new Promise((resolve, reject) => {
      redisClient.get(key, (error, value) => {
        if (!error) {
          resolve(value || null);
        } else {
          console.error('[Redis] Get redis key error', key, error);
          reject(error);
        }
      });
    });
  }

  /**
   * 设置 Redis 键值
   * @param redisClient Redis 客户端
   * @param key 键名
   * @param value 键值
   * @param ttl 过期时间（秒）
   * @returns 操作结果
   */
  function setRedisValue(
    redisClient: Redis,
    key: string,
    value: string,
    ttl: number
  ): Promise<string> {
    return new Promise((resolve, reject) => {
      redisClient.set(key, value, 'EX', ttl, (error, returnValue) => {
        if (!error) {
          resolve(returnValue || '');
        } else {
          console.error('[Redis] Set redis key error', key, value, ttl, error);
          reject(error);
        }
      });
    });
  }

  /**
   * 删除 Redis 键
   * @param redisClient Redis 客户端
   * @param key 键名
   */
  function deleteRedisKey(redisClient: Redis, key: string): Promise<void> {
    return new Promise<void>((resolve, reject) => {
      redisClient.del(key, (error) => {
        if (!error) {
          resolve();
        } else {
          console.error('[Redis] Delete redis key error', key, error);
          reject(error);
        }
      });
    });
  }

  return {
    getRedisValue,
    setRedisValue,
    deleteRedisKey,
  };
}

export default createRedisUtils;

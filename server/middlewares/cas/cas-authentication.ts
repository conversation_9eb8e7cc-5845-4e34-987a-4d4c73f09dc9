/**
 * CAS 认证中间件
 */
import url from 'node:url';
import type { Request, Response, NextFunction } from 'express';
import CASAuthentication from 'cas-authentication';
import type { CasAuthenticationOptions } from './types';

/**
 * CAS 认证类型枚举
 */
enum AUTH_TYPE {
  BOUNCE = 0,
  BOUNCE_REDIRECT = 1,
  BLOCK = 2,
}

/**
 * CAS 认证实例接口
 */
interface CasAuthenticationInstance {
  // 方法
  bounce: (req: Request, res: Response, next: NextFunction) => void;
  bounceRedirect: (req: Request, res: Response, next: NextFunction) => void;
  block: (req: Request, res: Response, next: NextFunction) => void;
  logout: (req: Request, res: Response, next: NextFunction) => void;
  _handleTicket: (req: Request, res: Response, next: NextFunction) => void;
  _login: (req: Request, res: Response) => void;
  _handle: (
    req: Request,
    res: Response,
    next: NextFunction,
    authType: AUTH_TYPE
  ) => void;

  // 属性
  cas_url: string;
  service_url: string;
  cas_version: string;
  renew: boolean;
  is_dev_mode: boolean;
  dev_mode_user: string;
  dev_mode_info: { attributes: Record<string, unknown> };
  session_name: string;
  session_info: string;
  destroy_session: boolean;
  cas_port: number;
}

/**
 * CAS 认证构造函数类型
 */
type CasAuthenticationConstructorType = new (
  options: CasAuthenticationOptions
) => CasAuthenticationInstance;

/**
 * 创建 CAS 认证中间件
 * @returns 自定义 CAS 认证类
 */
export function createCasAuthentication(): CasAuthenticationConstructorType {
  /**
   * 重写 CASAuthentication 类的 _login 方法，去除跳转时的 renew 参数
   */
  CASAuthentication.prototype._login = function (req: Request, res: Response) {
    // 保存返回 URL 到会话中。如果设置了显式返回 URL 作为查询参数，则使用该参数。否则，使用请求中的 URL。
    const casReturnTo = req.query.returnTo || url.parse(req.originalUrl).path;
    req.session.cas_return_to = casReturnTo as string;

    // Set up the query parameters.
    const query = {
      service: this.service_url + url.parse(req.originalUrl).pathname,
    };

    // Redirect to the CAS login.
    res.redirect(
      this.cas_url +
        url.format({
          pathname: '/login',
          query,
        })
    );
  };

  /**
   * 重写 CASAuthentication 类的 _handle 方法，处理 CAS 认证请求
   */
  CASAuthentication.prototype._handle = function (
    req: Request,
    res: Response,
    next: NextFunction,
    authType: AUTH_TYPE
  ) {
    // 如果会话已通过 CAS 验证，则无需操作
    if (req.session[this.session_name as string]) {
      // 如果这是一个重定向，则重定向已认证的用户
      if (authType === AUTH_TYPE.BOUNCE_REDIRECT) {
        res.redirect(req.session.cas_return_to as string);
      }
      // 否则，允许他们继续请求
      else {
        next();
      }
    }
    // 如果开发模式处于活动状态，将 CAS 用户设置为指定的开发用户
    else if (this.is_dev_mode) {
      req.session[this.session_name as string] = this.dev_mode_user;
      req.session[this.session_info as string] = this.dev_mode_info;
      next();
    }
    // 如果认证类型是 BLOCK，则简单地发送 401 响应
    else if (authType === AUTH_TYPE.BLOCK) {
      console.log(
        new Date().toISOString(),
        '[CAS] Authentication is blocked',
        req.url,
        req.cookies,
        req.session
      );
      res.sendStatus(401);
    }
    // 如果查询字符串中有 CAS 票据，则使用 CAS 服务器验证它
    else if (req.query && req.query.ticket) {
      // 修复由于没有session里cas_return_to的跳转问题
      if (!req.session.cas_return_to) {
        req.session.cas_return_to =
          this.service_url + url.parse(req.url).pathname;
      }

      this._handleTicket(req, res, next);
    }
    // 否则，重定向用户到 CAS 登录页面
    else {
      // 已变更为应用错误处理控制是否需要登录
      // this._login(req, res, next);
      next();
    }
  };

  /**
   * 重写 CASAuthentication 类的 logout 方法，支持跳转时增加 service 参数
   */
  CASAuthentication.prototype.logout = function (
    req: Request,
    res: Response,
    _next: NextFunction
  ) {
    // 如果设置了选项，则销毁整个会话
    if (this.destroy_session) {
      req.session.destroy((err: Error) => {
        if (err) {
          console.log('[CAS] Session destroy error', err);
        }
      });
    }
    // 否则，只销毁 CAS 会话变量
    else {
      delete req.session[this.session_name as string];
      if (this.session_info) {
        delete req.session[this.session_info as string];
      }
    }

    // 重定向客户端到 CAS 登出页面
    if (req.query.service) {
      res.redirect(
        `${this.cas_url}/logout?service=${encodeURIComponent(req.query.service as string)}`
      );
    } else {
      res.redirect(`${this.cas_url}/logout`);
    }
  };

  /**
   * 自定义 CAS 认证类，扩展原始 CASAuthentication 类
   * @param options CAS 认证选项
   */
  function SubCasAuthentication(
    this: CasAuthenticationInstance,
    options: CasAuthenticationOptions
  ) {
    CASAuthentication.call(this, options);

    // 支持端口号配置
    const parsed_cas_url = url.parse(this.cas_url);

    this.cas_port = parsed_cas_url.port
      ? parseInt(parsed_cas_url.port)
      : parsed_cas_url.protocol === 'https:'
        ? 443
        : 80;
  }

  // 继承原型
  SubCasAuthentication.prototype = Object.create(CASAuthentication.prototype);
  SubCasAuthentication.prototype.constructor = SubCasAuthentication;

  // 使用类型断言将函数转换为构造函数类型
  return SubCasAuthentication as unknown as CasAuthenticationConstructorType;
}

export default createCasAuthentication;

/**
 * CAS 中间件工具函数
 */
import { parseString } from 'xml2js';

/**
 * 创建 CAS 中间件工具函数
 * @returns CAS 中间件工具函数集合
 */
export function createUtils() {
  /**
   * 日志记录函数
   * @param debug 是否开启调试模式
   * @param args 日志参数
   */
  function log(debug: boolean, ...args: unknown[]) {
    if (debug) {
      console.log('[cas-middleware][DEBUG]', ...args);
    }
  }

  /**
   * 将字符串转换为 Base64 编码
   * @param str 原始字符串
   * @returns Base64 编码字符串
   */
  function btoa(str: string): string {
    return Buffer.from(str).toString('base64');
  }

  /**
   * 将 Base64 编码字符串解码为原始字符串
   * @param str Base64 编码字符串
   * @returns 原始字符串
   */
  function atob(str: string): string {
    return Buffer.from(str, 'base64').toString('ascii');
  }

  /**
   * 检查访问令牌是否已过期
   * @param param0 包含过期时间的对象
   * @returns 是否已过期
   */
  function accessTokenHasExpired({
    expiresIn,
  }: {
    expiresIn?: string;
  }): boolean {
    if (expiresIn) {
      return Date.now() > new Date(expiresIn).getTime();
    }

    return true;
  }

  /**
   * 将 XML 字符串转换为 JSON 对象
   * @param xmlString XML 字符串
   * @returns 解析后的 JSON 对象
   */
  function getJsonFromXML(xmlString: string): Promise<Record<string, unknown>> {
    return new Promise((resolve, reject) => {
      parseString(xmlString, (error, result) => {
        if (!error) {
          resolve(result);
        } else {
          reject(error);
        }
      });
    });
  }

  return {
    log,
    btoa,
    atob,
    accessTokenHasExpired,
    getJsonFromXML,
  };
}

export default createUtils;

/**
 * CAS 中间件集合
 */
import createCasAuthentication from './cas-authentication';
import createCasTicketMiddlewares from './cas-ticket';
import createTokenMiddlewares from './token';
import createLoginStatusMiddleware from './login-status';

/**
 * 创建 CAS 中间件集合
 * @returns CAS 中间件集合
 */
export function createCASMiddlewares() {
  return {
    createCasAuthentication,
    createCasTicketMiddlewares,
    createTokenMiddlewares,
    createLoginStatusMiddleware,
  };
}

export default createCASMiddlewares;

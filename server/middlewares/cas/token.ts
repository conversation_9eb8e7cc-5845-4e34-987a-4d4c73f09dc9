/**
 * 令牌中间件
 */
import type { Request, Response, NextFunction } from 'express';
import axios from 'axios';
import { nanoid } from 'nanoid';
import crypto from 'crypto';
import URI from 'urijs';
import qs from 'qs';
import type Redlock from 'redlock';

/**
 * 错误代码枚举
 *
 * GET_TOKEN_FAILED: 获取令牌失败
 * REFRESH_TOKEN_FAILED: 刷新令牌失败
 */
enum ErrorCode {
  GET_TOKEN_FAILED = 'F_GET_TOKEN_FAILED',
  REFRESH_TOKEN_FAILED = 'F_REFRESH_TOKEN_FAILED',
}

/**
 * 错误接口
 */
interface TokenError extends Error {
  response?: {
    status: number;
    data: unknown;
  };
  code?: string;
}

/**
 * 会话中的令牌信息
 */
interface SessionToken {
  accessToken: string;
  refreshToken: string;
  expiresIn: string;
  userCategory?: string;
}

/**
 * 令牌中间件选项接口
 */
export interface TokenMiddlewareOptions {
  /**
   * 客户端ID
   */
  clientId: string;

  /**
   * 客户端密钥
   */
  clientSecret: string;

  /**
   * 基础URL
   */
  baseUrl: string;

  /**
   * CAS用户会话键名
   */
  casUserSessionKey: string;

  /**
   * 令牌会话键名
   */
  tokenSessionKey?: string;

  /**
   * 访问令牌Cookie键名
   */
  accessTokenCookieKey?: string;

  /**
   * Cookie路径
   */
  cookiePath?: string;

  /**
   * Redis前缀
   */
  redisPrefix?: string;

  /**
   * Redlock实例
   */
  redlock: Redlock;
}

/**
 * 创建令牌中间件
 * @param options 中间件选项
 * @returns 令牌中间件集合
 */
export function createTokenMiddlewares(options: TokenMiddlewareOptions) {
  const {
    clientId,
    clientSecret,
    baseUrl,
    casUserSessionKey,
    tokenSessionKey,
    redisPrefix,
    redlock,
  } = options;
  const config_clientId = clientId;
  const config_clientSecret = clientSecret;
  const config_baseUrl = baseUrl;
  const config_casUserSessionKey = casUserSessionKey;
  const config_tokenSessionKey = tokenSessionKey || 'appToken';
  const config_redisPrefix = redisPrefix || '';

  /**
   * 计算MD5哈希值
   * @param str 输入字符串
   * @returns MD5哈希值
   */
  function calculateMd5(str: string): string {
    return crypto.createHash('md5').update(str).digest('hex');
  }

  /**
   * 申请令牌
   * @param username 用户名
   * @returns 令牌响应
   */
  function fetchToken(username: string) {
    const data = {
      clientId: config_clientId,
      nonce: calculateMd5(nanoid()),
      username,
    };

    const dataString = qs.stringify(data, { delimiter: '' });
    const raw = `${dataString}${config_clientSecret}`;
    const signature = calculateMd5(raw);
    Object.assign(data, {
      signature,
    });

    const uri = new URI(config_baseUrl);
    uri.segment('oauth2/user/accessToken');
    return axios.post(uri.toString(), qs.stringify(data), {
      headers: {
        Accept: 'application/json',
        'Content-Type': 'application/x-www-form-urlencoded',
      },
    });
  }

  /**
   * 刷新令牌
   * @param refreshToken_ 刷新令牌
   * @returns 令牌响应
   */
  function fetchRefreshToken(refreshToken: string) {
    const data = { refreshToken };

    const uri = new URI(config_baseUrl);
    uri.segment('oauth2/refreshToken');
    return axios.post(uri.toString(), qs.stringify(data), {
      headers: {
        Accept: 'application/json',
        'Content-Type': 'application/x-www-form-urlencoded',
      },
    });
  }

  /**
   * 检查令牌是否已过期
   * @param authInfo 认证信息
   * @returns 是否已过期
   */
  function isExpired(tokenInfo: SessionToken): boolean {
    const { expiresIn } = tokenInfo;
    if (expiresIn) {
      return Date.now() > new Date(expiresIn).getTime();
    }

    return true;
  }

  /**
   * 保存令牌信息到会话
   * @param req 请求对象
   * @param tokenInfo 认证信息
   * @returns 保存结果
   */
  function saveAuthTokenToSession(
    req: Request,
    tokenInfo: SessionToken
  ): Promise<void> {
    return new Promise<void>((resolve, reject) => {
      const { accessToken, refreshToken, expiresIn, userCategory } = tokenInfo;
      const interval =
        typeof expiresIn === 'number'
          ? expiresIn * 1000
          : parseInt(expiresIn) * 1000;
      const expires = new Date(Date.now() + interval).toISOString();

      req.session[config_tokenSessionKey] = {
        accessToken,
        refreshToken,
        expiresIn: expires,
        userCategory,
      };
      req.session.save((error) => {
        if (error) {
          console.error(
            '[Token] Save token to session error',
            (error as Error).stack || ''
          );
          reject(error);
        } else {
          resolve();
        }
      });
    });
  }

  /**
   * 阻止令牌中间件
   * 当用户未登录时返回401状态码
   */
  function blockTokenMiddleware(
    req: Request,
    res: Response,
    next: NextFunction
  ) {
    if (req.xhr) {
      if (
        !req.session ||
        (req.session && !req.session[config_casUserSessionKey])
      ) {
        res.sendStatus(401);
        return;
      }
    }

    next();
  }

  /**
   * 获取令牌并存储
   * @param req 请求对象
   */
  async function fetchTokenAndStore(req: Request): Promise<void> {
    try {
      const response = await fetchToken(
        req.session[config_casUserSessionKey] as string
      );
      const { success, model } = response.data;
      if (!success) {
        const error = new Error('Get token failed') as TokenError;
        error.response = response;
        throw error;
      }

      await saveAuthTokenToSession(req, model);
    } catch (error) {
      console.error('[Token] Get token error', (error as Error).stack || '');
      throw error;
    }
  }

  /**
   * 刷新令牌并存储
   * @param req 请求对象
   */
  async function refreshTokenAndStore(req: Request): Promise<void> {
    try {
      const response = await fetchRefreshToken(
        (req.session[config_tokenSessionKey] as SessionToken).refreshToken
      );
      const { success, model } = response.data;
      if (!success) {
        const error = new Error('Refresh token failed') as TokenError;
        error.response = response;
        throw error;
      }

      await saveAuthTokenToSession(req, model);
    } catch (error) {
      console.error(
        '[Token] Refresh token error',
        (error as Error).stack || ''
      );
      throw error;
    }
  }

  /**
   * 重新加载会话
   * @param req 请求对象
   * @returns 重新加载结果
   */
  function reloadSession(req: Request): Promise<void> {
    return new Promise((resolve, reject) => {
      try {
        req.session.reload(resolve);
      } catch (error) {
        reject(error);
      }
    });
  }

  /**
   * 清除会话登录信息
   * @param req 请求对象
   */
  function clearSessionLoginInfo(req: Request) {
    if (req.session) {
      if (req.session[config_casUserSessionKey]) {
        delete req.session[config_casUserSessionKey];
      }

      if (req.session[config_tokenSessionKey]) {
        delete req.session[config_tokenSessionKey];
      }
    }
  }

  /**
   * 更新令牌中间件
   * 检查并更新令牌
   */
  async function renewTokenMiddleware(
    req: Request,
    res: Response,
    next: NextFunction
  ) {
    // 如果没有配置TOKEN_API_BASE_URL，跳过Token获取
    if (
      !config_baseUrl ||
      config_baseUrl.includes('undefined') ||
      !config_baseUrl.startsWith('http')
    ) {
      next();
      return;
    }

    if (req.session && req.session[config_casUserSessionKey]) {
      if (!req.session[config_tokenSessionKey]) {
        try {
          await fetchTokenAndStore(req);
        } catch (error) {
          clearSessionLoginInfo(req);

          if (req.xhr) {
            const err = error as TokenError;
            if (err.response && err.response.status && err.response.data) {
              res.status(err.response.status);
              res.json(err.response.data);
              res.end();
              return;
            }

            res.status(500);
            res.json({
              success: false,
              errorCode: ErrorCode.GET_TOKEN_FAILED,
            });
            res.end();
            return;
          }

          const err = new Error((error as Error).message) as TokenError;
          err.code = ErrorCode.GET_TOKEN_FAILED; // 请注意，这里是node使用的错误码，不是接口返回的错误码
          next(err);
          return;
        }
      } else if (
        isExpired(req.session[config_tokenSessionKey] as SessionToken)
      ) {
        const resource = `${config_redisPrefix}locks:account:${req.session[config_casUserSessionKey]}`;
        const ttl = 3000;
        try {
          const lock = await redlock.lock(resource, ttl);
          await reloadSession(req);
          if (isExpired(req.session[config_tokenSessionKey] as SessionToken)) {
            await refreshTokenAndStore(req);
          }

          await redlock.unlock(lock);
        } catch (error) {
          clearSessionLoginInfo(req);

          if (req.xhr) {
            const err = error as TokenError;
            if (err.response && err.response.status && err.response.data) {
              res.status(err.response.status);
              res.json(err.response.data);
              res.end();
              return;
            }

            res.status(500);
            res.json({
              success: false,
              errorCode: ErrorCode.REFRESH_TOKEN_FAILED,
            });
            res.end();
            return;
          }

          const err = new Error((error as Error).message) as TokenError;
          err.code = ErrorCode.REFRESH_TOKEN_FAILED; // 请注意，这里是node使用的错误码，不是接口返回的错误码
          next(err);
          return;
        }
      }
    }

    next();
  }

  /**
   * 替换请求令牌中间件
   * 将令牌添加到请求头
   */
  function replaceRequestTokenMiddleware(
    req: Request,
    _res: Response,
    next: NextFunction
  ) {
    if (
      req.xhr &&
      req.session &&
      req.session[config_casUserSessionKey] &&
      req.session[config_tokenSessionKey]
    ) {
      const sessionToken = req.session[config_tokenSessionKey] as SessionToken;
      if (sessionToken.accessToken) {
        Object.assign(req.headers, {
          authorization: sessionToken.accessToken,
        });
      }
    }

    next();
  }

  /**
   * 重新生成会话
   * @param req 请求对象
   * @returns 重新生成结果
   */
  function regenerateSession(req: Request): Promise<void> {
    return new Promise<void>((resolve) => {
      req.session.regenerate((error) => {
        if (error) {
          console.error(
            '[Token] Session regenerate error',
            (error as Error).stack || ''
          );
        }

        resolve();
      });
    });
  }

  /**
   * 删除令牌中间件
   * 清除会话中的令牌信息
   */
  async function removeTokenMiddleware(
    req: Request,
    _res: Response,
    next: NextFunction
  ) {
    clearSessionLoginInfo(req);
    await regenerateSession(req);
    next();
  }

  return {
    blockTokenMiddleware,
    renewTokenMiddleware,
    replaceRequestTokenMiddleware,
    removeTokenMiddleware,
  };
}

export default createTokenMiddlewares;

/**
 * 登录状态中间件
 */
import type { Request, Response, NextFunction } from 'express';

/**
 * 登录状态中间件选项接口
 */
export interface LoginStatusMiddlewareOptions {
  /**
   * CAS 用户会话键名
   */
  casUserSessionKey: string;

  /**
   * 登录状态键名
   */
  loggedInKey?: string;
}

/**
 * 创建登录状态中间件
 * @param options 中间件选项
 * @returns 登录状态中间件
 */
export function createLoginStatusMiddleware(
  options: LoginStatusMiddlewareOptions
) {
  const { casUserSessionKey, loggedInKey } = options;
  const config_casUserSessionKey = casUserSessionKey;
  const config_loggedInKey = loggedInKey || 'appLoggedIn';

  if (!casUserSessionKey) {
    throw new Error('casUserSessionKey config option required');
  }

  /**
   * 登录状态中间件
   * 将用户登录状态添加到 res.locals 中
   */
  return function loginStatusMiddleware(
    req: Request,
    res: Response,
    next: NextFunction
  ) {
    if (!req.xhr) {
      Object.assign(res.locals, {
        [config_loggedInKey]: !!req.session[config_casUserSessionKey],
      });
    }

    next();
  };
}

export default createLoginStatusMiddleware;

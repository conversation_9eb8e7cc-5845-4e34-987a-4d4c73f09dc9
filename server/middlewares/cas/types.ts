/**
 * CAS 中间件类型定义
 */
import type { Request, Response, NextFunction } from 'express';
import type { Redis } from 'ioredis';
import type Redlock from 'redlock';

/**
 * CAS 认证选项接口
 */
export interface CasAuthenticationOptions {
  /**
   * CAS 服务器 URL
   */
  cas_url: string;

  /**
   * 服务 URL
   */
  service_url: string;

  /**
   * CAS 版本
   */
  cas_version: string;

  /**
   * 会话信息名称
   */
  session_info: string;

  /**
   * 会话名称
   */
  session_name?: string;

  /**
   * 是否每次都重新认证
   */
  renew?: boolean;

  /**
   * 是否开发模式
   */
  is_dev_mode?: boolean;

  /**
   * 开发模式用户
   */
  dev_mode_user?: string;

  /**
   * 开发模式用户信息
   */
  dev_mode_info?: { attributes: Record<string, unknown> };

  /**
   * 是否销毁会话
   */
  destroy_session?: boolean;
}

/**
 * CAS 认证类接口
 */
export interface CasAuthenticationInstance {
  /**
   * 跳转到 CAS 登录页面
   */
  bounce(req: Request, res: Response, next: NextFunction): void;

  /**
   * 跳转到 CAS 登录页面并重定向
   */
  bounceRedirect(req: Request, res: Response, next: NextFunction): void;

  /**
   * 阻止未认证的请求
   */
  block(req: Request, res: Response, next: NextFunction): void;

  /**
   * 登出
   */
  logout(req: Request, res: Response, next: NextFunction): void;

  /**
   * 处理票据
   */
  _handleTicket(req: Request, res: Response, next: NextFunction): void;

  /**
   * 登录
   */
  _login(req: Request, res: Response): void;

  /**
   * 处理请求
   */
  _handle(
    req: Request,
    res: Response,
    next: NextFunction,
    authType: number
  ): void;
}

/**
 * CAS 认证构造函数
 */
export interface CasAuthenticationConstructor {
  new (options: CasAuthenticationOptions): CasAuthenticationInstance;
}

/**
 * CAS 票据中间件选项
 */
export interface CasTicketMiddlewareOptions {
  /**
   * Redis 客户端
   */
  redisClient: Redis;

  /**
   * CAS 用户会话键名
   */
  casUserSessionKey: string;

  /**
   * 令牌会话键名
   */
  tokenSessionKey?: string;
}

/**
 * 令牌中间件选项
 */
export interface TokenMiddlewareOptions {
  /**
   * 客户端 ID
   */
  clientId: string;

  /**
   * 客户端密钥
   */
  clientSecret: string;

  /**
   * 基础 URL
   */
  baseUrl: string;

  /**
   * CAS 用户会话键名
   */
  casUserSessionKey: string;

  /**
   * 令牌会话键名
   */
  tokenSessionKey?: string;

  /**
   * 访问令牌 Cookie 键名
   */
  accessTokenCookieKey?: string;

  /**
   * Cookie 路径
   */
  cookiePath?: string;

  /**
   * Redis 前缀
   */
  redisPrefix?: string;

  /**
   * Redlock 实例
   */
  redlock: Redlock;
}

/**
 * 登录状态中间件选项
 */
export interface LoginStatusMiddlewareOptions {
  /**
   * CAS 用户会话键名
   */
  casUserSessionKey: string;

  /**
   * 登录状态键名
   */
  loggedInKey?: string;
}

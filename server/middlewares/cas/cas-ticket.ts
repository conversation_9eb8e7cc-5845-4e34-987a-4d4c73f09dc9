/**
 * CAS 票据中间件
 */
import type { Request, Response, NextFunction } from 'express';
import type { Redis } from 'ioredis';
import createRedisUtils from './redis';
import createUtils from './utils';

/**
 * CAS 票据中间件选项接口
 */
export interface CasTicketMiddlewareOptions {
  /**
   * Redis 客户端
   */
  redisClient: Redis;

  /**
   * CAS 用户会话键名
   */
  casUserSessionKey: string;

  /**
   * 令牌会话键名
   */
  tokenSessionKey?: string;
}

/**
 * 创建 CAS 票据中间件
 * @param options 中间件选项
 * @returns CAS 票据中间件集合
 */
export function createCasTicketMiddlewares(
  options: CasTicketMiddlewareOptions
) {
  const { getRedisValue, setRedisValue, deleteRedisKey } = createRedisUtils();
  const { getJsonFromXML } = createUtils();

  const { redisClient, casUserSessionKey, tokenSessionKey } = options;
  const config_casUserSessionKey = casUserSessionKey;
  const config_tokenSessionKey = tokenSessionKey || 'appToken';

  const TICKET_PREFIX = 'tkt';
  const SESSION_PREFIX = 'sess';
  const DURATION = 60 * 60 * 24 * 30; // 保存30天，正常情况不会超过这个时间，实现自动清除

  if (!redisClient) {
    throw new Error('redisClient not configured');
  }

  if (!config_casUserSessionKey) {
    throw new Error('casUserSessionKey not configured');
  }

  /**
   * 获取票据键名
   * @param ticket 票据
   * @returns 票据键名
   */
  function getTicketKey(ticket: string): string {
    return `${TICKET_PREFIX}:${ticket}`;
  }

  /**
   * 获取会话键名
   * @param sessionId 会话ID
   * @returns 会话键名
   */
  function getSessionKey(sessionId: string): string {
    return `${SESSION_PREFIX}:${sessionId}`;
  }

  /**
   * CAS 票据会话映射中间件
   * 当有票据参数时，将票据和会话ID映射保存到 Redis 中
   */
  async function casTicketSessionMapperMiddleware(
    req: Request,
    res: Response,
    next: NextFunction
  ) {
    if (
      !req.xhr &&
      req.query &&
      req.query.ticket &&
      req.session &&
      req.session.id
    ) {
      try {
        // 当有ticket参数时，清除用户登录相关session
        if (req.session[config_casUserSessionKey]) {
          delete req.session[config_casUserSessionKey];
        }

        if (req.session[config_tokenSessionKey]) {
          delete req.session[config_tokenSessionKey];
        }

        // 仅保存30天
        const key = getTicketKey(req.query.ticket as string);
        await setRedisValue(redisClient, key, req.session.id, DURATION); // 保存30天，正常情况不会超过这个时间，实现自动清除
      } catch (error) {
        console.error(
          '[CAS] Save ticket and session id map error',
          (error as Error).stack || ''
        );
        throw error;
      }
    }

    next();
  }

  /**
   * CAS 单点登出中间件
   * 处理 CAS 服务器发送的登出请求
   */
  async function casSloMiddleware(
    req: Request,
    res: Response,
    next: NextFunction
  ) {
    if (req.body && req.body.logoutRequest) {
      try {
        const logoutRequest = await getJsonFromXML(req.body.logoutRequest);
        if (
          logoutRequest &&
          typeof logoutRequest['samlp:LogoutRequest'] === 'object' &&
          logoutRequest['samlp:LogoutRequest'] !== null &&
          'samlp:SessionIndex' in logoutRequest['samlp:LogoutRequest']
        ) {
          const sessionIndex =
            logoutRequest['samlp:LogoutRequest']['samlp:SessionIndex'];
          if (
            sessionIndex &&
            Array.isArray(sessionIndex) &&
            sessionIndex.length > 0
          ) {
            const ticketKey = getTicketKey(sessionIndex[0].trim());
            const sessionId = await getRedisValue(redisClient, ticketKey);
            if (sessionId) {
              const sessionKey = getSessionKey(sessionId);
              await deleteRedisKey(redisClient, sessionKey);
              await deleteRedisKey(redisClient, ticketKey);
            }
          }
        }

        res.sendStatus(201);
        return;
      } catch (error) {
        console.error(
          '[CAS] SLO middleware error',
          (error as Error).stack || ''
        );
        next(error);
        return;
      }
    }

    next();
  }

  return {
    casTicketSessionMapperMiddleware,
    casSloMiddleware,
  };
}

export default createCasTicketMiddlewares;

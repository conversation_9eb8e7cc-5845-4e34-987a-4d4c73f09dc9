import type { Request, Response, NextFunction } from 'express';

/**
 * 用户分类中间件选项接口
 */
export interface CreateUserCategoryMiddlewareOptions {
  /**
   * 令牌会话键名
   */
  tokenSessionKey?: string;

  /**
   * 默认用户分类
   */
  fallbackUserCategory?: string;
}

/**
 * 会话中的令牌信息
 */
interface SessionToken {
  accessToken: string;
  refreshToken: string;
  expiresIn: string;
  userCategory: string;
}

/**
 * 创建用户分类中间件
 *
 * @param options 中间件选项
 * @returns Express中间件函数
 */
export function createUserCategoryMiddleware(
  options: CreateUserCategoryMiddlewareOptions
) {
  const { tokenSessionKey, fallbackUserCategory } = options;

  const config_tokenSessionKey = tokenSessionKey || 'appToken';
  const config_fallbackUserCategory = fallbackUserCategory || 'TEACHER';

  return (req: Request, res: Response, next: NextFunction): void => {
    let userCategory: string;
    if (
      req.session &&
      req.session[config_tokenSessionKey] &&
      (req.session[config_tokenSessionKey] as SessionToken).userCategory
    ) {
      userCategory = (req.session[config_tokenSessionKey] as SessionToken)
        .userCategory as string;
    } else {
      userCategory = config_fallbackUserCategory;
    }

    Object.assign(res.locals, {
      userCategory,
    });

    next();
  };
}

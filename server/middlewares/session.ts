import session from 'express-session';
import { RedisStore } from 'connect-redis';
import Redis from 'ioredis';
import type { RequestHandler } from 'express';
import type { C<PERSON><PERSON><PERSON><PERSON> } from 'node:crypto';

/**
 * Session中间件配置选项
 */
export interface CreateSessionMiddlewareOptions {
  /**
   * Redis客户端实例
   */
  redisClient: Redis;

  /**
   * Session密钥
   */
  secret: CipherKey | CipherKey[];

  /**
   * Session名称
   */
  name: string;

  /**
   * 是否只在HTTPS下发送Cookie
   */
  secure: boolean;

  /**
   * Cookie的SameSite属性
   */
  sameSite: boolean | 'lax' | 'strict' | 'none';

  /**
   * 是否保存未修改的会话
   */
  saveUninitialized?: boolean;

  /**
   * 是否在每次请求时强制保存会话
   */
  resave?: boolean;
}

/**
 * 创建Session中间件
 *
 * @param options Session中间件配置选项
 * @returns Express中间件
 */
export function createSessionMiddleware(
  options: CreateSessionMiddlewareOptions
): RequestHandler {
  const {
    redisClient,
    secret,
    name,
    secure,
    sameSite,
    resave = false,
    saveUninitialized = false,
  } = options;

  // 创建Redis存储
  const redisStore = new RedisStore({
    client: redisClient,
  });

  // 创建Session中间件
  return session({
    store: redisStore,
    secret,
    name,
    cookie: {
      secure,
      sameSite,
      httpOnly: true,
    },
    saveUninitialized,
    resave,
  });
}

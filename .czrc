{"path": "cz-conventional-changelog", "disableScopeLowerCase": false, "disableSubjectLowerCase": false, "maxHeaderWidth": 100, "maxLineWidth": 100, "defaultType": "", "defaultScope": "", "defaultSubject": "", "defaultBody": "", "defaultIssues": "", "types": {"feat": {"description": "✨ 新功能: 新增功能或特性", "title": "Features"}, "fix": {"description": "🐛 修复: 修复Bug", "title": "Bug Fixes"}, "docs": {"description": "📚 文档: 文档变更", "title": "Documentation"}, "style": {"description": "💎 格式: 代码格式（不影响代码运行的变动）", "title": "Styles"}, "refactor": {"description": "📦 重构: 代码重构（既不是新增功能，也不是修改bug的代码变动）", "title": "Code Refactoring"}, "perf": {"description": "🚀 性能: 性能优化", "title": "Performance Improvements"}, "test": {"description": "🚨 测试: 增加测试", "title": "Tests"}, "chore": {"description": "🔧 构建: 构建过程或辅助工具的变动", "title": "Chores"}, "revert": {"description": "⏪ 回退: 回退代码", "title": "Reverts"}, "build": {"description": "📦 打包: 打包构建", "title": "Builds"}, "ci": {"description": "👷 CI: CI相关变更", "title": "Continuous Integration"}}}